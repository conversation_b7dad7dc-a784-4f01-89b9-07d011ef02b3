<template>
  <div 
    class="document-preview"
    :class="{
      'sidebar-mode': mode === 'sidebar',
      'fullscreen-mode': mode === 'fullscreen',
      'visible': visible
    }"
  >
    <!-- Header with controls -->
    <div class="preview-header">
      <div class="preview-title">
        <q-icon name="description" class="q-mr-sm" />
        
        <!-- Document selector - different approaches for different modes -->
        <!-- Sidebar mode: Use dropdown -->
        <div v-if="hasMultipleDocuments && mode === 'sidebar'" class="document-selector-wrapper">
          <q-select
            v-model="selectedDocumentId"
            :options="documentOptions"
            emit-value
            map-options
            dense
            outlined
            class="q-mr-sm document-selector"
            style="min-width: 200px"
          >
            <template v-slot:prepend>
              <q-icon name="description" />
            </template>
            <template v-slot:option="scope">
              <q-item v-bind="scope.itemProps">
                <q-item-section>
                  <q-item-label>{{ scope.opt.label }}</q-item-label>
                  <q-item-label caption>{{ scope.opt.description }}</q-item-label>
                </q-item-section>
              </q-item>
            </template>
          </q-select>
        </div>

        <!-- Fullscreen mode: Use button to toggle document sidebar -->
        <div v-else-if="hasMultipleDocuments && mode === 'fullscreen'" class="document-selector-fullscreen">
          <q-btn
            flat
            dense
            :icon="showDocumentSidebar ? 'menu_open' : 'menu'"
            class="q-mr-sm"
            @click="toggleDocumentSidebar"
          >
            <q-tooltip>{{ showDocumentSidebar ? 'Hide' : 'Show' }} Documents</q-tooltip>
          </q-btn>
          <span class="document-title-fullscreen">{{ previewState.title }}</span>
        </div>
        
        <!-- Single document title or fallback -->
        <span v-else>{{ props.title || previewState.title }}</span>
      </div>
      <div class="preview-controls">
        <!-- Fullscreen button: always visible in sidebar mode, leftmost and colored for prominence -->
        <q-btn
          v-if="mode === 'sidebar'"
          flat
          round
          color="primary"
          icon="fullscreen"
          size="md"
          @click="openFullscreen"
          class="q-mr-xs"
          style="margin-right: 8px;"
        >
          <q-tooltip>Open in fullscreen</q-tooltip>
        </q-btn>
        <q-btn
          v-if="mode === 'fullscreen'"
          flat
          round
          icon="fullscreen_exit"
          size="sm"
          @click="exitFullscreen"
          class="q-mr-xs"
        >
          <q-tooltip>Exit fullscreen</q-tooltip>
        </q-btn>
        <q-btn
          flat
          round
          icon="content_copy"
          size="sm"
          @click="copyContent"
          class="q-mr-xs"
        >
          <q-tooltip>Copy HTML</q-tooltip>
        </q-btn>
        <q-btn
          flat
          round
          icon="download"
          size="sm"
          @click="downloadContent"
          class="q-mr-xs"
        >
          <q-tooltip>Download as HTML</q-tooltip>
        </q-btn>
        <q-btn
          flat
          round
          icon="close"
          size="sm"
          @click="closePreview"
        >
          <q-tooltip>Close preview</q-tooltip>
        </q-btn>
      </div>
    </div>

    <!-- Content area with document sidebar for fullscreen -->
    <div class="preview-content" :class="{ 'with-sidebar': showDocumentSidebar && mode === 'fullscreen' }">
      
      <!-- Document sidebar for fullscreen mode -->
      <div v-if="mode === 'fullscreen' && showDocumentSidebar" class="document-sidebar">
        <div class="sidebar-header">
          <q-icon name="description" class="q-mr-sm" />
          <span class="sidebar-title">Documents</span>
          <q-btn 
            flat 
            round 
            dense 
            icon="close" 
            size="sm"
            @click="showDocumentSidebar = false"
            class="q-ml-auto"
          />
        </div>
        <q-list class="document-list">
          <q-item 
            v-for="doc in localDocuments" 
            :key="doc.id"
            clickable
            :active="doc.id === activeDocumentId"
            @click="switchDocument(doc.id)"
            class="document-item"
          >
            <q-item-section avatar>
              <q-icon name="description" />
            </q-item-section>
            <q-item-section>
              <q-item-label>{{ doc.title }}</q-item-label>
              <q-item-label caption>{{ doc.source }}</q-item-label>
            </q-item-section>
          </q-item>
        </q-list>
      </div>

      <!-- Main content area -->
      <div class="main-content">
        <div v-if="loading" class="loading-container">
          <div class="loading-content">
            <q-spinner-dots size="48px" color="primary" />
            <div class="q-mt-md text-h6 text-grey-7">Loading document...</div>
            <div class="q-mt-sm text-caption text-grey-5">
              {{ loadingMessage }}
            </div>
            <q-linear-progress 
              v-if="showProgress"
              :value="loadingProgress" 
              color="primary" 
              size="4px"
              class="q-mt-md loading-progress"
            />
          </div>
        </div>
        
        <div v-else-if="error" class="error-container">
          <div class="error-content">
            <q-icon name="error_outline" size="56px" color="negative" class="error-icon" />
            <div class="q-mt-md text-h6 text-negative">{{ error }}</div>
            <div class="q-mt-sm text-body2 text-grey-6">
              {{ errorDetails }}
            </div>
            <div class="error-actions q-mt-lg">
              <q-btn 
                unelevated
                color="primary" 
                @click="retry" 
                class="q-mr-sm"
                :loading="retrying"
              >
                <q-icon name="refresh" class="q-mr-xs" />
                Try Again
              </q-btn>
              <q-btn 
                flat
                color="grey" 
                @click="closePreview"
              >
                Close
              </q-btn>
            </div>
          </div>
        </div>

        <!-- Direct HTML rendering instead of iframe -->
        <div
          v-else-if="sanitizedHtml"
          ref="htmlContainer"
          class="html-content-container"
          :class="{ 'content-loading': contentLoading }"
          v-html="sanitizedHtml"
        />

        <div v-else class="empty-container">
          <div class="empty-content">
            <q-icon name="description" size="56px" color="grey-4" class="empty-icon" />
            <div class="q-mt-md text-h6 text-grey-5">No content to preview</div>
            <div class="q-mt-sm text-body2 text-grey-6">
              Select a document or generate HTML content to begin
            </div>
          </div>
        </div>
      </div>
    </div>



    <!-- Resize handle for sidebar mode -->
    <div 
      v-if="mode === 'sidebar' && resizable"
      class="resize-handle"
      @mousedown="startResize"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useQuasar } from 'quasar'
import { previewState, switchToDocument } from '../utils/documentPreview'
import DOMPurify from 'dompurify'

interface Props {
  htmlContent?: string
  mode?: 'sidebar' | 'fullscreen'
  visible?: boolean
  resizable?: boolean
  title?: string
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'sidebar',
  visible: false,
  resizable: true,
  title: 'Document Preview'
})

const emit = defineEmits<{
  close: []
  fullscreen: []
  exitFullscreen: []
  resize: [width: number]
}>()

const router = useRouter()
const $q = useQuasar()

// Reactive state
const loading = ref(false)
const error = ref('')
const htmlContainer = ref<HTMLElement>()

// Enhanced loading states
const loadingMessage = ref('Preparing document...')
const showProgress = ref(false)
const loadingProgress = ref(0)
const contentLoading = ref(false)
const retrying = ref(false)
const errorDetails = ref('')

// DOMPurify configuration for safe HTML rendering with interactivity
const purifyConfig = {
  // Allow common HTML tags needed for document rendering including interactive elements
  ALLOWED_TAGS: [
    'div', 'p', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'ul', 'ol', 'li', 'a', 'strong', 'em', 'b', 'i', 'u',
    'table', 'thead', 'tbody', 'tr', 'td', 'th',
    'img', 'br', 'hr', 'blockquote', 'pre', 'code',
    'section', 'article', 'header', 'footer', 'nav', 'main',
    'figure', 'figcaption', 'dl', 'dt', 'dd',
    // Interactive elements
    'button', 'input', 'textarea', 'select', 'option', 'form', 'label',
    'details', 'summary', 'canvas', 'svg', 'path', 'g', 'circle', 'rect',
    // Media elements
    'video', 'audio', 'source', 'iframe'
  ],
  // Allow safe attributes including event handlers
  ALLOWED_ATTR: [
    'class', 'id', 'style', 'src', 'alt', 'title', 'href', 'target',
    'width', 'height', 'colspan', 'rowspan', 'align', 'valign',
    // Interactive attributes
    'onclick', 'onchange', 'onsubmit', 'onload', 'type', 'value', 'name',
    'placeholder', 'required', 'disabled', 'checked', 'selected',
    // Data attributes
    'data-*',
    // SVG attributes
    'viewBox', 'xmlns', 'd', 'fill', 'stroke', 'stroke-width',
    // Media attributes
    'controls', 'autoplay', 'loop', 'muted', 'preload'
  ],
  // Allow data URLs and blob URLs for media
  ALLOWED_URI_REGEXP: /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp|data|blob):|[^a-z]|[a-z+.-]+(?:[^a-z+.-:]|$))/i,
  // Keep relative URLs
  KEEP_CONTENT: true,
  // Return DOM elements instead of string
  RETURN_DOM: false,
  // Allow custom processing
  WHOLE_DOCUMENT: false,
  // Allow unknown protocols for better compatibility
  ALLOW_UNKNOWN_PROTOCOLS: true
}

// Sanitized HTML content using DOMPurify
const sanitizedHtml = computed(() => {
  const htmlContent = props.htmlContent || previewState.htmlContent
  if (!htmlContent) return ''
  
  try {
    // First, extract the body content if it's a full HTML document
    const bodyMatch = htmlContent.match(/<body[^>]*>([\s\S]*?)<\/body>/i)
    const contentToSanitize = bodyMatch ? bodyMatch[1] : htmlContent
    
    // Ensure we have content to sanitize
    if (!contentToSanitize) return ''
    
    // Sanitize the HTML content
    const sanitized = DOMPurify.sanitize(contentToSanitize, purifyConfig)
    
    return sanitized
  } catch (err) {
    console.error('Failed to sanitize HTML:', err)
    // Set error in nextTick to avoid side effect in computed
    void nextTick(() => {
      error.value = 'Failed to process HTML content'
    })
    return ''
  }
})

// Phase 2: Core Data Structure Setup
const localDocuments = computed(() => {
  const docs = previewState.documents
  return docs
})

const activeDocumentId = computed(() => {
  const activeId = previewState.activeDocumentId
  return activeId
})

const hasMultipleDocuments = computed(() => {
  const hasMultiple = localDocuments.value.length > 1
  return hasMultiple
})

// Phase 4: Dropdown Options Generation
const documentOptions = computed(() => {
  const options = localDocuments.value.map(doc => ({
    label: doc.title,
    value: doc.id,
    description: `From ${doc.source}`
  }))
  
  return options
})

const selectedDocumentId = computed({
  get: () => activeDocumentId.value,
  set: (value: string) => {
    switchDocument(value)
  }
})

// Fullscreen document sidebar state
const showDocumentSidebar = ref(false)

const toggleDocumentSidebar = () => {
  showDocumentSidebar.value = !showDocumentSidebar.value
}

// Methods
const previousRoute = ref<string>('')

const openFullscreen = async () => {
  // Store current route for proper return navigation
  previousRoute.value = router.currentRoute.value.fullPath
  await router.push({
    name: 'DocumentPreview'
    // No need to pass content via query params - using global state now
  })
  emit('fullscreen')
}

const exitFullscreen = async () => {
  // Return to previous route if available, otherwise go back in history
  if (previousRoute.value) {
    await router.push(previousRoute.value)
  } else {
    router.go(-1)
  }
  emit('exitFullscreen')
}

const closePreview = () => {
  emit('close')
}

// Phase 3: Document Selection Logic
const switchDocument = (documentId: string) => {
  try {
    switchToDocument(documentId)
  } catch (error) {
    console.error(`Failed to switch document: ${documentId}`, error)
    $q.notify({
      type: 'negative',
      message: 'Failed to switch document',
      position: 'top'
    })
  }
}

const copyContent = async () => {
  try {
    const content = props.htmlContent || previewState.htmlContent
    await navigator.clipboard.writeText(content)
    $q.notify({
      type: 'positive',
      message: 'HTML content copied to clipboard',
      position: 'top'
    })
  } catch {
    $q.notify({
      type: 'negative',
      message: 'Failed to copy content',
      position: 'top'
    })
  }
}

const downloadContent = () => {
  const content = props.htmlContent || previewState.htmlContent
  if (!content) return
  
  const blob = new Blob([content], { type: 'text/html' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `document-${Date.now()}.html`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  
  $q.notify({
    type: 'positive',
    message: 'Document downloaded',
    position: 'top'
  })
}

const retry = async () => {
  retrying.value = true
  error.value = ''
  errorDetails.value = ''
  loading.value = true
  loadingMessage.value = 'Retrying...'
  showProgress.value = true
  
  // Simulate loading progress
  loadingProgress.value = 0
  const progressInterval = setInterval(() => {
    if (loadingProgress.value < 90) {
      loadingProgress.value += 10
    }
  }, 100)
  
  try {
    // Wait a bit to show loading state
    await new Promise(resolve => setTimeout(resolve, 1000))
    loadingProgress.value = 100
    
    await nextTick(() => {
      loading.value = false
      retrying.value = false
      showProgress.value = false
      clearInterval(progressInterval)
    })
  } catch (err) {
    clearInterval(progressInterval)
    loading.value = false
    retrying.value = false
    showProgress.value = false
    error.value = 'Failed to load document'
    errorDetails.value = err instanceof Error ? err.message : 'Unknown error occurred'
  }
}

const onContentLoad = () => {
  loading.value = false
  error.value = ''
}

// Resize functionality for sidebar mode
const isResizing = ref(false)
const currentWidth = ref(400)

const startResize = (e: MouseEvent) => {
  isResizing.value = true
  document.addEventListener('mousemove', doResize)
  document.addEventListener('mouseup', stopResize)
  e.preventDefault()
}

const doResize = (e: MouseEvent) => {
  if (!isResizing.value) return
  
  const container = document.querySelector('.document-preview')
  if (!container) return
  
  const rect = container.getBoundingClientRect()
  const newWidth = e.clientX - rect.left
  
  if (newWidth >= 300 && newWidth <= 800) {
    currentWidth.value = newWidth
    emit('resize', newWidth)
  }
}

const stopResize = () => {
  isResizing.value = false
  document.removeEventListener('mousemove', doResize)
  document.removeEventListener('mouseup', stopResize)
}

// Watch for content changes
watch(() => props.htmlContent || previewState.htmlContent, (newContent) => {
  if (newContent) {
    loading.value = true
    error.value = ''
    
    // Use nextTick to ensure DOM is ready
    void nextTick(() => {
      onContentLoad()
    })
  }
}, { immediate: true })

// Enhanced styles and interactivity setup
onMounted(() => {
  // Add custom styles for better HTML rendering
  const style = document.createElement('style')
  style.textContent = `
    .html-content-container {
      line-height: 1.6;
      color: #333;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    .html-content-container img {
      max-width: 100%;
      height: auto;
    }
    .html-content-container table {
      border-collapse: collapse;
      width: 100%;
      margin: 1rem 0;
    }
    .html-content-container th, .html-content-container td {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: left;
    }
    .html-content-container th {
      background-color: #f2f2f2;
      font-weight: bold;
    }
    .html-content-container pre {
      background-color: #f5f5f5;
      padding: 1rem;
      border-radius: 4px;
      overflow-x: auto;
    }
    .html-content-container code {
      background-color: #f5f5f5;
      padding: 0.2em 0.4em;
      border-radius: 3px;
      font-size: 0.9em;
    }
    .html-content-container blockquote {
      border-left: 4px solid #ddd;
      margin: 1rem 0;
      padding-left: 1rem;
      color: #666;
    }
    .html-content-container button {
      background: #1976d2;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      margin: 4px;
    }
    .html-content-container button:hover {
      background: #1565c0;
    }
    .html-content-container input, .html-content-container textarea, .html-content-container select {
      border: 1px solid #ddd;
      padding: 8px;
      border-radius: 4px;
      margin: 4px;
    }
    .html-content-container details {
      border: 1px solid #ddd;
      border-radius: 4px;
      padding: 8px;
      margin: 8px 0;
    }
    .html-content-container summary {
      cursor: pointer;
      font-weight: bold;
      padding: 4px;
    }
  `
  document.head.appendChild(style)
  
  // Setup event delegation for interactive content
  setupInteractivity()
})

// Setup interactivity for rendered HTML content
const setupInteractivity = () => {
  // Event delegation for forms and inputs
  document.addEventListener('submit', (e) => {
    if (htmlContainer.value?.contains(e.target as Node)) {
      e.preventDefault()
      console.log('Form submitted in document preview:', e.target)
      $q.notify({
        type: 'info',
        message: 'Form submission intercepted for security',
        position: 'top'
      })
    }
  })
  
  // Handle button clicks in document
  document.addEventListener('click', (e) => {
    if (htmlContainer.value?.contains(e.target as Node) && (e.target as HTMLElement).tagName === 'BUTTON') {
      const button = e.target as HTMLButtonElement
      console.log('Button clicked in document preview:', button.textContent)
      $q.notify({
        type: 'positive',
        message: `Button "${button.textContent}" clicked`,
        position: 'top'
      })
    }
  })
}

// Cleanup on unmount
onUnmounted(() => {
  // No blob URLs to cleanup anymore
})
</script>

<style scoped lang="scss">
.document-preview {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  
  &.sidebar-mode {
    border: 1px solid #e0e0e0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    width: 400px;
    min-width: 300px;
    max-width: 800px;
    min-height: 500px; /* Ensure minimum height for better visibility */
  }
  
  &.fullscreen-mode {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 9999;
    border-radius: 0;
  }
  
  &:not(.visible) {
    display: none;
  }
}

.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f5f5f5;
  border-bottom: 1px solid #e0e0e0;
  min-height: 48px;
  flex-shrink: 0; /* Prevent header from shrinking */
}

.document-selector {
  max-width: 250px; /* Limit dropdown width */
}

.document-selector-wrapper {
  display: flex;
  align-items: center;
}

.document-selector-fullscreen {
  display: flex;
  align-items: center;
  
  .document-title-fullscreen {
    font-weight: 500;
    color: #1976d2;
  }
}

.preview-content {
  flex: 1;
  position: relative;
  overflow: hidden;
  
  &.with-sidebar {
    display: flex;
    flex-direction: row;
  }
}

.document-sidebar {
  width: 300px;
  background: #f8f9fa;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  
  .sidebar-header {
    display: flex;
    align-items: center;
    padding: 16px;
    background: #fff;
    border-bottom: 1px solid #e0e0e0;
    font-weight: 500;
    color: #1976d2;
    
    .sidebar-title {
      flex: 1;
    }
  }
  
  .document-list {
    flex: 1;
    overflow-y: auto;
    
    .document-item {
      &.q-item--active {
        background: rgba(25, 118, 210, 0.1);
        color: #1976d2;
      }
      
      &:hover {
        background: rgba(0, 0, 0, 0.04);
      }
    }
  }
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* Allow flex child to shrink */
  
  /* Ensure full height when no sidebar */
  .preview-content:not(.with-sidebar) & {
    height: 100%;
  }
}

.preview-title {
  display: flex;
  align-items: center;
  font-weight: 500;
  color: #1976d2;
}

.preview-controls {
  display: flex;
  align-items: center;
}

/* Removed duplicate - styles moved above */

.loading-container,
.error-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px 20px;
  text-align: center;
}

.loading-content,
.error-content,
.empty-content {
  max-width: 400px;
  width: 100%;
}

.loading-progress {
  width: 100%;
  border-radius: 2px;
  overflow: hidden;
}

.error-icon,
.empty-icon {
  opacity: 0.8;
  margin-bottom: 8px;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.html-content-container {
  width: 100%;
  height: 100%;
  overflow: auto;
  padding: 16px;
  transition: opacity 0.3s ease;
}

.html-content-container.content-loading {
  opacity: 0.6;
  pointer-events: none;
}

/* Enhanced loading animations */
@keyframes pulse-subtle {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.loading-content {
  animation: pulse-subtle 2s ease-in-out infinite;
}

.error-content {
  animation: fadeInUp 0.4s ease-out;
}

.empty-content {
  animation: fadeInUp 0.4s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .document-preview.sidebar-mode {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    max-width: none;
    border-radius: 0;
    z-index: 9998;
  }
  
  .resize-handle {
    display: none;
  }
}
</style> 