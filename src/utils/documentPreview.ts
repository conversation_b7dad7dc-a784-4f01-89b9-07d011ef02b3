import { reactive } from 'vue'

export interface DocumentItem {
  id: string
  title: string
  content: string
  source: string // Message ID or source identifier
  timestamp: number
}

export interface DocumentPreviewState {
  isVisible: boolean
  htmlContent: string
  mode: 'sidebar' | 'fullscreen'
  title: string
  documents: DocumentItem[]
  activeDocumentId: string
}

// Global state for document preview
const previewState = reactive<DocumentPreviewState>({
  isVisible: false,
  htmlContent: '',
  mode: 'sidebar',
  title: 'Document Preview',
  documents: [],
  activeDocumentId: ''
})

/**
 * Detects if a text contains HTML content that should be previewed
 * Uses enhanced pattern matching and confidence scoring
 */
export function detectHTMLContent(text: string): boolean {
  if (!text || typeof text !== 'string') return false
  
  // Quick checks for obvious HTML indicators
  const quickChecks = [
    /<!doctype\s+html/i,                    // DOCTYPE declaration
    /<html[\s\S]*?<\/html>/i,               // Full HTML document
    /<body[\s\S]*?<\/body>/i,               // Body tag
    /<head[\s\S]*?<\/head>/i,               // Head tag
  ]
  
  for (const pattern of quickChecks) {
    if (pattern.test(text)) return true
  }
  
  // Enhanced pattern matching for structured content
  const structurePatterns = [
    /<(?:div|section|article|main|header|footer|nav)[\s\S]*?>[\s\S]*?<\/(?:div|section|article|main|header|footer|nav)>/i,
    /<(?:h[1-6])[\s\S]*?>[\s\S]*?<\/h[1-6]>[\s\S]*<(?:p|div)[\s\S]*?>[\s\S]*?<\/(?:p|div)>/i,
    /<table[\s\S]*?>[\s\S]*?<\/table>/i,
    /<form[\s\S]*?>[\s\S]*?<\/form>/i,
    /<ul[\s\S]*?>[\s\S]*?<\/ul>/i,
    /<ol[\s\S]*?>[\s\S]*?<\/ol>/i,
  ]
  
  // Check for multiple structural elements
  let structureMatches = 0
  for (const pattern of structurePatterns) {
    if (pattern.test(text)) structureMatches++
  }
  
  if (structureMatches >= 2) return true
  
  // Count block-level elements for better detection
  const blockElements = [
    'div', 'p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 
    'section', 'article', 'main', 'header', 'footer', 'nav',
    'ul', 'ol', 'li', 'table', 'tr', 'td', 'th',
    'form', 'fieldset', 'blockquote', 'pre'
  ]
  
  let blockElementCount = 0
  let totalHTMLTags = 0
  
  for (const element of blockElements) {
    const regex = new RegExp(`<${element}(?:\\s[^>]*)?>`, 'gi')
    const matches = text.match(regex)
    if (matches) {
      blockElementCount += matches.length
    }
  }
  
  // Count all HTML tags
  const allTagsRegex = /<[a-zA-Z][^>]*>/g
  const allTags = text.match(allTagsRegex)
  if (allTags) {
    totalHTMLTags = allTags.length
  }
  
  // Enhanced decision logic
  if (blockElementCount >= 3) return true
  if (totalHTMLTags >= 8 && blockElementCount >= 2) return true
  
  // Check for interactive elements
  const interactiveElements = ['input', 'button', 'select', 'textarea', 'a']
  let interactiveCount = 0
  
  for (const element of interactiveElements) {
    const regex = new RegExp(`<${element}(?:\\s[^>]*)?>`, 'gi')
    const matches = text.match(regex)
    if (matches) {
      interactiveCount += matches.length
    }
  }
  
  if (interactiveCount >= 3) return true
  
  return false
}

/**
 * Extracts HTML content from a text that might contain other content
 */
export function extractHTMLContent(text: string): string {
  if (!text) return ''
  
  // First, try to extract HTML from markdown code blocks
  const codeBlockPatterns = [
    /```html\s*([\s\S]*?)\s*```/gi,
    /```\s*(<!DOCTYPE\s+html[\s\S]*?)\s*```/gi,
    /```\s*(<html[\s\S]*?<\/html>)\s*```/gi,
  ]
  
  for (const pattern of codeBlockPatterns) {
    const match = text.match(pattern)
    if (match && match[1]) {
      text = match[1].trim()
      break
    }
  }
  
  // Try to find HTML document boundaries
  const htmlMatch = text.match(/<html[\s\S]*?<\/html>/i)
  if (htmlMatch) return htmlMatch[0]
  
  // Try to find complete document with DOCTYPE
  const doctypeMatch = text.match(/<!DOCTYPE\s+html[\s\S]*?<\/html>/i)
  if (doctypeMatch) return doctypeMatch[0]
  
  // Try to find body content
  const bodyMatch = text.match(/<body[\s\S]*?<\/body>/i)
  if (bodyMatch) return `<html><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"></head>${bodyMatch[0]}</html>`
  
  // Try to find a large block of HTML content
  const htmlBlockRegex = /(<(?:div|section|article|main|header|footer|nav)[^>]*>[\s\S]*?<\/(?:div|section|article|main|header|footer|nav)>)/gi
  const blocks = text.match(htmlBlockRegex)
  
  if (blocks && blocks.length > 0) {
    const combinedBlocks = blocks.join('\n')
    return `<html><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Generated Document</title></head><body>${combinedBlocks}</body></html>`
  }
  
  // If no clear structure, wrap everything in basic HTML
  return `<html><head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><title>Generated Document</title></head><body>${text}</body></html>`
}

/**
 * Extracts a meaningful title from HTML content
 * Uses multiple strategies and fallbacks for best results
 */
export function extractHTMLTitle(html: string): string {
  if (!html) return 'Untitled Document'
  
  // Strategy 1: Look for <title> tag first
  const titleMatch = html.match(/<title[^>]*>(.*?)<\/title>/i)
  if (titleMatch && titleMatch[1]) {
    const title = cleanTitle(titleMatch[1])
    if (title && title.length > 3 && title.length < 100) {
      return title
    }
  }
  
  // Strategy 2: Look for heading tags in order of importance
  const headingSelectors = ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']
  for (const selector of headingSelectors) {
    const headingMatch = html.match(new RegExp(`<${selector}[^>]*>(.*?)</${selector}>`, 'i'))
    if (headingMatch && headingMatch[1]) {
      const title = cleanTitle(headingMatch[1])
      if (title && title.length > 3 && title.length < 80) {
        return `${title}`
      }
    }
  }
  
  // Strategy 3: Look for semantic content indicators
  const semanticSelectors = [
    { pattern: /<header[^>]*>[\s\S]*?<(?:h[1-6]|p)[^>]*>(.*?)<\/(?:h[1-6]|p)>[\s\S]*?<\/header>/i, weight: 'high' },
    { pattern: /<article[^>]*>[\s\S]*?<(?:h[1-6])[^>]*>(.*?)<\/(?:h[1-6])>[\s\S]*?<\/article>/i, weight: 'high' },
    { pattern: /<main[^>]*>[\s\S]*?<(?:h[1-6])[^>]*>(.*?)<\/(?:h[1-6])>[\s\S]*?<\/main>/i, weight: 'high' },
  ]
  
  for (const selector of semanticSelectors) {
    const match = html.match(selector.pattern)
    if (match && match[1]) {
      const title = cleanTitle(match[1])
      if (title && title.length > 3 && title.length < 80) {
        return title
      }
    }
  }
  
  // Strategy 4: Look for strong/bold text that might be titles
  const emphasisPatterns = [
    /<strong[^>]*>(.*?)<\/strong>/gi,
    /<b[^>]*>(.*?)<\/b>/gi,
    /<em[^>]*>(.*?)<\/em>/gi,
  ]
  
  for (const pattern of emphasisPatterns) {
    const matches = html.match(pattern)
    if (matches) {
      for (const match of matches) {
        const textMatch = match.match(/>([^<]+)</i)
        if (textMatch && textMatch[1]) {
          const title = cleanTitle(textMatch[1])
          if (title && title.length > 5 && title.length < 60 && !title.toLowerCase().includes('click')) {
            return title
          }
        }
      }
    }
  }
  
  // Strategy 5: Look for the first substantial paragraph
  const paragraphMatch = html.match(/<p[^>]*>(.*?)<\/p>/i)
  if (paragraphMatch && paragraphMatch[1]) {
    const title = cleanTitle(paragraphMatch[1])
    if (title && title.length > 10 && title.length < 100) {
      // Truncate if too long
      return title.length > 50 ? title.substring(0, 47) + '...' : title
    }
  }
  
  // Strategy 6: Extract from first meaningful text content
  const textContent = html.replace(/<[^>]*>/g, ' ').replace(/\s+/g, ' ').trim()
  if (textContent) {
    const sentences = textContent.split(/[.!?]/)
    for (const sentence of sentences) {
      const cleaned = sentence.trim()
      if (cleaned.length > 10 && cleaned.length < 80) {
        return cleaned.length > 50 ? cleaned.substring(0, 47) + '...' : cleaned
      }
    }
    
    // Fallback to first 50 characters
    const firstText = textContent.substring(0, 50).trim()
    if (firstText.length > 10) {
      return firstText + (textContent.length > 50 ? '...' : '')
    }
  }
  
  // Strategy 7: Generate title based on content type
  if (html.includes('<table')) return 'Data Table'
  if (html.includes('<form')) return 'Interactive Form'
  if (html.includes('<canvas')) return 'Canvas Visualization'
  if (html.includes('<svg')) return 'SVG Graphics'
  if (html.includes('<video') || html.includes('<audio')) return 'Media Content'
  if (html.includes('<nav')) return 'Navigation Menu'
  
  return 'HTML Document'
}

/**
 * Cleans and normalizes title text
 */
function cleanTitle(title: string): string {
  if (!title) return ''
  
  // Remove HTML tags if any remain
  let cleaned = title.replace(/<[^>]*>/g, '')
  
  // Decode HTML entities
  const entityMap: Record<string, string> = {
    '&amp;': '&',
    '&lt;': '<',
    '&gt;': '>',
    '&quot;': '"',
    '&#39;': "'",
    '&nbsp;': ' ',
    '&mdash;': '—',
    '&ndash;': '–',
    '&hellip;': '…'
  }
  
  Object.keys(entityMap).forEach(entity => {
    const replacement = entityMap[entity]
    if (replacement) {
      cleaned = cleaned.replace(new RegExp(entity, 'g'), replacement)
    }
  })
  
  // Clean up whitespace and special characters
  cleaned = cleaned
    .replace(/\s+/g, ' ')          // Multiple spaces to single space
    .replace(/^\W+|\W+$/g, '')     // Remove leading/trailing non-word chars
    .trim()
  
  // Remove common prefixes that aren't useful
  const prefixesToRemove = [
    'welcome to ',
    'this is ',
    'here is ',
    'check out ',
    'example: ',
    'demo: ',
    'test: '
  ]
  
  const lowerCleaned = cleaned.toLowerCase()
  for (const prefix of prefixesToRemove) {
    if (lowerCleaned.startsWith(prefix)) {
      cleaned = cleaned.substring(prefix.length).trim()
      break
    }
  }
  
  // Capitalize first letter if it's all lowercase
  if (cleaned === cleaned.toLowerCase() && cleaned.length > 0) {
    cleaned = cleaned.charAt(0).toUpperCase() + cleaned.slice(1)
  }
  
  return cleaned
}

/**
 * Adds a document to the collection and optionally shows it
 */
export function addDocument(htmlContent: string, source: string, title?: string, makeActive: boolean = true): string {
  // Normalize source for streaming documents to prevent duplicates
  let normalizedSource = source
  if (source.startsWith('stream_') || source.startsWith('stream_auto_')) {
    // Use a consistent source ID for streaming content by using the session
    normalizedSource = 'streaming_current'
  }
  
  // Check if document from this normalized source already exists
  const existingDoc = previewState.documents.find(doc => {
    // For streaming sources, check if we have any streaming document
    if (normalizedSource === 'streaming_current') {
      return doc.source === 'streaming_current' || doc.source.startsWith('stream_') || doc.source.startsWith('stream_auto_')
    }
    return doc.source === normalizedSource
  })
  
  if (existingDoc) {
    // Update the existing document with new content
    existingDoc.content = htmlContent
    existingDoc.source = normalizedSource
    existingDoc.timestamp = Date.now()
    
    // Update title if provided or extract a better one
    if (title) {
      existingDoc.title = title
    } else {
      const extractedTitle = extractHTMLTitle(htmlContent)
      if (extractedTitle && extractedTitle !== 'Untitled Document') {
        existingDoc.title = extractedTitle
      }
    }
    
    if (makeActive) {
      switchToDocument(existingDoc.id)
    }
    return existingDoc.id
  }
  
  const id = `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  
  // Use provided title, or extract from HTML, or use fallback
  let documentTitle = title
  if (!documentTitle) {
    const extractedTitle = extractHTMLTitle(htmlContent)
    if (extractedTitle && extractedTitle !== 'Untitled Document') {
      documentTitle = extractedTitle
    } else {
      // Create a fallback title with message information
      const messageMatch = source.match(/message_(\d+)/)
      if (messageMatch) {
        // For message sources, create a meaningful title with message number
        const messageNumber = previewState.documents.length + 1 // Sequential number
        documentTitle = `Untitled Document (From message ${messageNumber})`
      } else {
        // For other sources (like streaming), use a simple fallback
        documentTitle = 'Untitled Document'
      }
    }
  }
  
  const newDocument: DocumentItem = {
    id,
    title: documentTitle,
    content: htmlContent,
    source: normalizedSource,
    timestamp: Date.now()
  }
  
  previewState.documents.push(newDocument)
  
  if (makeActive) {
    previewState.activeDocumentId = id
    previewState.htmlContent = htmlContent
    previewState.title = documentTitle
    previewState.isVisible = true
  }
  
  return id
}

/**
 * Switches to a specific document
 */
export function switchToDocument(documentId: string) {
  const document = previewState.documents.find(doc => doc.id === documentId)
  if (document) {
    previewState.activeDocumentId = documentId
    previewState.htmlContent = document.content
    previewState.title = document.title
    previewState.isVisible = true
  }
}

/**
 * Removes a document from the collection
 */
export function removeDocument(documentId: string) {
  const index = previewState.documents.findIndex(doc => doc.id === documentId)
  if (index >= 0) {
    previewState.documents.splice(index, 1)
    
    // If the removed document was active, switch to another or hide
    if (previewState.activeDocumentId === documentId) {
      if (previewState.documents.length > 0) {
        const newActiveDoc = previewState.documents[Math.max(0, index - 1)]
        if (newActiveDoc) {
          switchToDocument(newActiveDoc.id)
        }
      } else {
        hideDocumentPreview()
      }
    }
  }
}

/**
 * Shows the document preview with the given content (legacy function)
 */
export function showDocumentPreview(htmlContent: string, mode: 'sidebar' | 'fullscreen' = 'sidebar', title?: string) {
  // For backward compatibility, add as a new document
  const source = `legacy_${Date.now()}`
  // Let addDocument handle title extraction if no title provided
  const finalTitle = title || undefined
  addDocument(htmlContent, source, finalTitle, true)
  previewState.mode = mode
}

/**
 * Hides the document preview
 */
export function hideDocumentPreview() {
  previewState.isVisible = false
}

/**
 * Resets the document preview state completely
 */
export function resetDocumentPreview() {
  previewState.isVisible = false
  previewState.htmlContent = ''
  previewState.mode = 'sidebar'
  previewState.title = 'Document Preview'
  previewState.documents = []
  previewState.activeDocumentId = ''
}

/**
 * Gets the current preview state
 */
export function getPreviewState() {
  return previewState
}

/**
 * Analyzes a chat message to detect and extract all HTML content
 */
export function analyzeMessageForHTML(message: string): {
  hasHTML: boolean
  extractedHTML: string
  confidence: number
  allDocuments?: Array<{
    html: string
    title: string
    confidence: number
  }>
} {
  const allDocuments = extractAllHTMLDocuments(message)
  
  if (allDocuments.length === 0) {
    return {
      hasHTML: false,
      extractedHTML: '',
      confidence: 0
    }
  }
  
  // Return the first document for backward compatibility
  const firstDoc = allDocuments[0]
  if (!firstDoc) {
    return {
      hasHTML: false,
      extractedHTML: '',
      confidence: 0
    }
  }
  
  return {
    hasHTML: true,
    extractedHTML: firstDoc.html,
    confidence: firstDoc.confidence,
    allDocuments
  }
}

/**
 * Extracts all HTML documents from a message text
 */
export function extractAllHTMLDocuments(text: string): Array<{
  html: string
  title: string
  confidence: number
}> {
  if (!text) return []
  
  const documents: Array<{ html: string; title: string; confidence: number }> = []
  
  // Patterns to find HTML documents (ordered by specificity)
  const htmlPatterns = [
    // Code blocks containing HTML (most specific first)
    /```html\s*([\s\S]*?)\s*```/gi,
    /```\s*(<!DOCTYPE\s+html[\s\S]*?)\s*```/gi,
    /```\s*(<html[\s\S]*?<\/html>)\s*```/gi,
    // Full HTML documents with DOCTYPE
    /<!DOCTYPE\s+html[\s\S]*?<\/html>/gi,
    // HTML documents with html tags
    /<html[\s\S]*?<\/html>/gi,
  ]
  
  // Track already found content to avoid duplicates
  const foundContent = new Set<string>()
  
  for (const pattern of htmlPatterns) {
    let match
    pattern.lastIndex = 0 // Reset regex state
    
    while ((match = pattern.exec(text)) !== null) {
      let htmlContent = match[1] || match[0] // Get captured group or full match
      htmlContent = htmlContent.trim()

      // Create a more robust content hash for deduplication
      const contentHash = normalizeHTMLForDeduplication(htmlContent)
      
      // Skip if we've already found this content
      if (foundContent.has(contentHash)) {
        continue
      }
      
      foundContent.add(contentHash)
      
      // Calculate confidence based on HTML complexity
      let confidence = 0.5
      
      if (htmlContent.includes('<html')) confidence += 0.3
      if (htmlContent.includes('<head')) confidence += 0.1
      if (htmlContent.includes('<body')) confidence += 0.1
      if (htmlContent.includes('<style') || htmlContent.includes('<link')) confidence += 0.1
      if (htmlContent.includes('<script')) confidence += 0.1
      
      // Extract title
      const title = extractHTMLTitle(htmlContent) || `Document ${documents.length + 1}`
      
      documents.push({
        html: htmlContent,
        title,
        confidence: Math.min(confidence, 1.0)
      })
    }
  }
  
  // Look for div-heavy content that might be HTML components (only if no other HTML found)
  if (documents.length === 0) {
    const divPattern = /<div[\s\S]*?<\/div>/gi
    const divMatches = text.match(divPattern)
    
    if (divMatches && divMatches.length >= 3) {
      // Multiple div blocks might be HTML content
      const combinedHTML = divMatches.join('\n')
      if (combinedHTML.length > 200) {
        const title = extractHTMLTitle(combinedHTML) || 'HTML Component'
        documents.push({
          html: combinedHTML,
          title,
          confidence: 0.6
        })
      }
    }
  }
  
  return documents
}

/**
 * Sanitizes HTML content for safe preview
 */
export function sanitizeHTML(html: string): string {
  if (!html) return ''
  
  let sanitized = html
  
  // Remove potentially dangerous script tags
  sanitized = sanitized.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
  
  // Remove dangerous event handlers
  sanitized = sanitized.replace(/\son\w+\s*=\s*["'][^"']*["']/gi, '')
  
  // Remove dangerous attributes
  const dangerousAttrs = ['onload', 'onerror', 'onclick', 'onmouseover', 'onfocus', 'onblur']
  for (const attr of dangerousAttrs) {
    const regex = new RegExp(`\\s${attr}\\s*=\\s*["'][^"']*["']`, 'gi')
    sanitized = sanitized.replace(regex, '')
  }
  
  return sanitized
}

// Helper function to normalize HTML content for deduplication
function normalizeHTMLForDeduplication(html: string): string {
  return html
    // Remove DOCTYPE declaration
    .replace(/<!DOCTYPE\s+html[^>]*>/gi, '')
    // Normalize whitespace - replace multiple spaces/newlines with single space
    .replace(/\s+/g, ' ')
    // Trim leading/trailing whitespace
    .trim()
    // Convert to lowercase for case-insensitive comparison
    .toLowerCase();
}

// Export the reactive state for use in components
export { previewState } 