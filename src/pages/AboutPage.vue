<template>
  <q-layout>
    <q-page-container>
      <q-page class="row justify-center q-pa-md">
        <div class="col-12 col-md-8">
          <!-- About Section -->
          <div class="conversation-container">
            <div v-html="markdown.render(aboutContent)"></div>
          </div>

          <!-- Terms and Conditions Section -->
          <div class="conversation-container">
            <div v-html="markdown.render(termsContent)"></div>
          </div>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import MarkdownIt from 'markdown-it';
import MarkdownItKatex from 'markdown-it-katex';

const markdown = new MarkdownIt();
markdown.use(MarkdownItKatex);

defineOptions({
  name: 'AboutPage',
});

// Markdown content for About section
const aboutContent = ref(`
## About Bytewise

Developed by the **HKBU Language Centre**, Bytewise is an AI-powered platform designed to enhance learning, teaching, and language development through cutting-edge generative AI technology. Led by **Dr. <PERSON>** (Contact: [<EMAIL>](mailto:<EMAIL>)), the platform integrates advanced models from providers like OpenAI, Google Gemini, Anthropic, and Meta to support ethical academic exploration.
`);

// Markdown content for Terms and Conditions
const termsContent = ref(`
## Terms and Conditions for Using "Bytewise"

### **1. Introduction**
Bytewise is an AI-powered educational platform developed by the HKBU Language Centre to support academic and language development activities. It integrates generative AI models from third-party providers, including OpenAI, Anthropic, and others (see full list in Appendix A). Users acknowledge that AI-generated content may contain inaccuracies or biases and must exercise oversight over its use.

### **2. Compliance with Policies & Laws**
Use of Bytewise is governed by:
- **[HKBU’s Policy on Using AI-Based Services](https://securedoc.hkbu.edu.hk/staffOnly/ics/integration/Policy_on_using_AI-based_services.pdf)**
- **[Data Governance Policy](https://securedoc.hkbu.edu.hk/staffOnly/ics/integration/Data_Governance_Policy.pdf)**
- **[Privacy Management Programme](https://securedoc.hkbu.edu.hk/staffOnly/page.php?f=ics/integration/Privacy_Management_Programme.pdf)**
- **[Data Leakage Prevention Policy](https://ito.hkbu.edu.hk/content/ito/en/_jcr_content.ssocheck.json?pathPdf=/content/dam/hongkongbaptistuniversity/ito-assets/doc/student-staff/security/guides/DLP-Policy.pdf)**
- **[Privacy Policy Statement](https://bupdpo.hkbu.edu.hk/)**.

Users agree to comply with all applicable laws, including data privacy, intellectual property, and anti-discrimination regulations.

### **3. Authorised Use**
**Eligible Users**:
- **Students**: For coursework, language practice, or academic research.
- **Staff**: For teaching, research, or administrative tasks.

**Prohibited Uses**:
- Inputting **sensitive data** (e.g., personal information, passwords, restricted research data).
- Commercial activities, spam, or non-academic purposes.
- Generating unethical/illegal content (e.g., cheating materials, hate speech).

### **4. Ethical & Academic Standards**
Users must:
- Verify AI-generated content for accuracy, bias, and compliance with academic integrity policies.
- **Never cite AI-generated text** in formal university documents or submitted work without explicit permission.
- Ensure use aligns with the **[Principles for the Use of Generative AI Tools in Teaching and Learning and Assessment](https://ar.hkbu.edu.hk/student-services/learning-and-teaching/learning-and-teaching-strategy-and-policies/principles-for-the-use-of-generative-ai-tools-in-teaching-and-learning-and-assessment)** and FERPA-equivalent standards.

### **5. Data Privacy & Security**
- **Restricted Inputs**: No confidential or proprietary data (HKBU’s or stakeholders’) may be entered.
- **Activity Logging**: Inputs/outputs are logged for misuse monitoring.
- **Incident Reporting**: Suspected breaches must be reported immediately to Dr. Simon Wang.

### **6. Tracking Learning Outcomes**
Teachers will have access to students’ chatbot activity history to monitor learning progress and ensure effective use of Bytewise. This activity history includes but is not limited to:
- The content of conversations between the student and the chatbot.
- **Metadata**, such as the number of conversation turns, total word count in interactions, and other usage patterns.
This information is collected solely for academic purposes and follows the university’s data privacy policies. Teachers will use it to track learning outcomes, provide feedback, and offer better guidance to students.

### **7. Limitation of Liability**
Bytewise and HKBU Language Centre disclaim responsibility for:
- Harm caused by reliance on AI-generated content.
- Third-party model errors, biases, or security breaches.
- Unauthorized account access due to user negligence.
Users indemnify the University against claims arising from their use.

### **8. Termination**
Access may be revoked without notice for:
- Academic misconduct (e.g., plagiarism, cheating).
- Unethical use (e.g., prompt injection attacks, unauthorized data scraping).
- Violations of usage quotas or policies.

### **9. Governing Law**
Governed by Hong Kong law. Disputes resolved exclusively in Hong Kong courts.

### **10. Privacy Policy**
Bytewise collects users' names, email addresses, roles, and chat history with chatbots and avatars. This information is used to provide and improve services, prevent fraud and misuse, and ensure compliance with applicable laws. Bytewise does not share user information with any external parties. However, Bytewise utilizes third-party services, including Supabase, HeyGen, and OpenRouter. As a result, user information may be shared with these service providers. For more details, please refer to the following policies: [Supabase Terms](https://supabase.com/terms), [Supabase Privacy](https://supabase.com/privacy), [HeyGen Terms](https://www.heygen.com/terms), [HeyGen Privacy](https://www.heygen.com/privacy), [OpenRouter Terms](https://openrouter.ai/terms), and [OpenRouter Privacy](https://openrouter.ai/privacy).

Bytewise implements and maintains technical and administrative security measures to protect user data from accidental loss and from unauthorized access, alteration, and disclosure. However, Bytewise cannot guarantee the absolute security of the data systems. In the event of a security breach, Bytewise will take reasonable measures to mitigate the loss and, if appropriate, notify affected individuals and take other actions in accordance with applicable laws and regulations.

### **11. User Content and User Customised Bots**
Bytewise allows users to create and share conversations, and to upload chatbot prompts or files within Bytewise or externally. All materials uploaded, submitted, displayed, or shared by users within Bytewise, as well as any results, answers, or content generated in response to users' prompts or inputs by bots on Bytewise, are collectively referred to as "User Content." Bytewise also allows users to create or add customized bots to the Bytewise platform, collectively referred to as "User Customised Bots."

User Customised Bots will be linked to users' Bytewise profiles. Creating or adding User Customised Bots is optional, but if users choose to do so, they take full responsibility for ensuring that their own User Content complies with Hong Kong laws and does not violate the aforementioned Terms, including but not limited to Bytewise's policies and the policies of any third parties or services used by User Customised Bots.

Bytewise reserves the right, at its sole discretion, to remove User Customised Bots if they violate these Terms, our policies, third-party policies, or any relevant legal requirements. Bytewise is not responsible for any User Customised Bots, User Content, or any content generated by User Customised Bots. Bytewise is not responsible for monitoring, censoring, or moderating User Customised Bots, and disclaims any liability arising from the content, actions, or use of such bots by any party.


### **Appendix A: AI Model Providers**
Bytewise integrates models from:
- OpenAI, Anthropic, Google Gemini, Meta Llama, DeepSeek, Qwen, MiniMax, xAI, and Perplexity.

### **Appendix B: HKBU Policy on AI-Based Services**
Key requirements from the **[University’s AI Policy](https://securedoc.hkbu.edu.hk/staffOnly/ics/integration/Policy_on_using_AI-based_services.pdf)** include:
1. **Ethical Oversight**: Ensure AI use aligns with university values.
2. **Prohibited Inputs**: No sensitive, confidential, or public-domain-restricted data.
3. **Legal Accountability**: Users bear full responsibility for outputs.
4. **Academic Integrity**: AI-generated content must not replace original work.
5. **Monitoring & Reporting**: Users must flag errors, bias, or misuse.
`);
</script>

<style scoped>
/* Reuse the same markdown styles from SessionPage */
:deep(.conversation-container h1) {
  font-size: 24px;
  font-weight: bold;
  line-height: 1.5rem;
  margin: 1.5rem 0;
}

:deep(.conversation-container h2) {
  font-size: 22px;
  font-weight: bold;
  line-height: 1.4rem;
  margin: 1.4rem 0;
}

:deep(.conversation-container h3) {
  font-size: 20px;
  font-weight: bold;
  line-height: 1.3rem;
  margin: 1.3rem 0;
}

:deep(.conversation-container pre) {
  max-width: 100%;
  overflow-x: auto;
  background: #f5f5f5;
  padding: 1rem;
  border-radius: 4px;
  margin: 1rem 0;
}

:deep(.conversation-container code) {
  font-family: monospace;
  white-space: pre-wrap;
  word-wrap: break-word;
}

:deep(.conversation-container p code) {
  background: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 0.9em;
}

:deep(.conversation-container ul),
:deep(.conversation-container ol) {
  padding-left: 1.5em;
  margin: 1em 0;
}

:deep(.conversation-container li) {
  margin: 0.5em 0;
}

:deep(.conversation-container p) {
  margin: 1em 0;
  line-height: 1.6;
}
</style>
