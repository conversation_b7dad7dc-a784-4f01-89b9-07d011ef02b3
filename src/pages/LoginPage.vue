<template>
  <q-layout>
    <q-header class="bg-white text-dark">
      <q-toolbar>
        <q-toolbar-title>
          <q-btn
            flat
            no-caps
            label="Bytewise"
            to="/start"
            class="text-weight-bold"
            style="font-size: 1.3125rem; padding: 0 10px"
          />
        </q-toolbar-title>
        <q-space />

        <q-btn flat no-caps label="Register" to="/register" />
      </q-toolbar>
    </q-header>
    <q-page-container class="bytewise-background-grey-100">
      <q-page class="row items-center justify-evenly q-pa-md">
        <!-- <q-img src="https://picsum.photos/400" class="col-5" /> -->

        <!-- <div class="col-4"> -->
        <div class="login-page-form">
          <div class="text-h4 text-weight-bold text-start q-mb-lg">Login Bytewise</div>
          <div class="text-h6 text-weight-light text-start q-mb-lg">Please login to continue</div>
          <q-input
            v-model="form.email"
            label="Your email"
            clearable
            type="email"
            :rules="[
              (val) => !!val || 'Email is required',
              (val) => /.+@.+\..+/.test(val) || 'Email must be valid',
            ]"
          />
          <q-input
            v-model="form.password"
            label="Your password"
            clearable
            type="password"
            :rules="[
              (val) => !!val || 'Password is required',
              (val) => val.length >= 6 || 'Password must be at least 6 characters',
            ]"
            @keyup.enter="onLogin"
          />

          <q-checkbox v-model="form.agreeToTerms">
            I agree to the
            <a href="/#/about" target="_blank" class="text-primary" @click.stop
              >Terms and Conditions
            </a>
          </q-checkbox>

          <q-btn
            class="q-mt-md"
            label="Login"
            @click="onLogin"
            color="black"
            text-color="white"
            style="width: 98%"
          />
          <!-- <q-btn class="q-mt-md " flat label="or connect with" style="width: 98%;color: #000000" />
          <q-btn class="q-mt-md" label="Sign in" @click="navigateToStu" color="black" text-color="white"
            style="width: 98%" /> -->
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { onMounted, ref } from 'vue';
import { apiLogin } from 'src/api/login';
import { onNotify } from 'src/utils/notifications';
import type { AxiosError } from 'axios';

const router = useRouter();
const $q = useQuasar();

const form = ref({
  email: '',
  password: '',
  agreeToTerms: true,
});

interface ErrorResponseData {
  detail?: string;
}

const onLogin = async () => {
  onNotify($q);

  if (!form.value.agreeToTerms) {
    $q.notify({
      type: 'negative',
      message: 'Please agree to the Terms and Conditions',
    });
    return;
  }

  if (!form.value.email) {
    $q.notify({
      type: 'negative',
      message: 'Email is required',
    });
    return;
  }

  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailPattern.test(form.value.email)) {
    $q.notify({
      type: 'negative',
      message: 'Email format is invalid',
    });
    return;
  }

  if (!form.value.password) {
    $q.notify({
      type: 'negative',
      message: 'Password is required',
    });
    return;
  }

  try {
    // Call API login
    const { userData } = await apiLogin({
      email: form.value.email,
      password: form.value.password,
    });
    // If success, redirect to student page
    $q.notify({
      type: 'positive',
      message: 'Login success',
    });
    if (userData['role_name'] === 'Teacher') {
      await router.push('/teacher/homepage');
    } else if (userData['role_name'] === 'Student') {
      await router.push('/student/homepage');
    }
  } catch (error) {
    // Catch error when login failed
    const axiosError = error as AxiosError<ErrorResponseData>;

    const errorMessage = axiosError.response?.data?.detail
      ? axiosError.response.data.detail
      : 'Login failed, please try again'; // Default error message

    $q.notify({
      type: 'negative',
      message: `Login failed, please try again. Error: ${errorMessage}`,
    });
  }
};

onMounted(() => {
  onNotify($q);
});

defineOptions({
  name: 'LoginPage',
});
</script>

<style lang="scss" scoped>
@import 'src/css/app.scss';
.login-page-form {
  width: 95%;
  max-width: 800px; // 限制最大宽度，避免超宽屏幕过散
  min-width: 300px; // 确保小屏幕有最小宽度
  background-color: map-get($white-colors, white);
  padding: 20px clamp(20px, 10vw, 80px); // 动态调整左右内边距
  text-align: center;
  border-radius: 10px;
  box-shadow: 0 2px 4px map-get($white-colors, shadow-light);
  margin: 0 auto; // 水平居中

  // 小屏幕进一步优化
  @media (max-width: 600px) {
    padding: 15px clamp(15px, 5vw, 30px); // 小屏幕减小内边距
  }

  // 暗模式适配
  @media (prefers-color-scheme: dark) {
    background-color: map-get($white-colors, bg-dark);
    box-shadow: 0 2px 4px map-get($white-colors, shadow-dark);
  }
}
</style>
