<template>
  <q-page v-if="!isFetching" class="q-pa-md">
    <!-- 课程标题区域 -->
    <div class="row items-center justify-between q-mb-lg">
      <div class="text-h3">{{ courseTitle }}</div>
    </div>

    <!-- 没有模块时的提示 -->
    <div v-if="moduleChatbotList.length === 0" class="text-h6 text-grey-6 text-center q-pa-xl">
      No modules available for this course
    </div>

    <!-- 模块列表 -->
    <div v-for="module in moduleCombinedList" :key="module.module_id" class="q-mb-xl">
      <q-separator spaced />

      <!-- 模块标题 -->
      <div class="row items-center justify-between q-py-md">
        <div class="text-h4">{{ module.module_title }}</div>
      </div>

      <!-- 没有聊天机器人时的提示 -->
      <div
        v-if="
          (module.chatbots.length === 1 &&
            !module.chatbots[0]?.chatbot_id &&
            !module.avatars[0]?.avatar_id) ||
          (module.chatbots.length === 0 && module.avatars.length === 0)
        "
        class="text-subtitle1 text-grey-6 text-center q-pa-md"
      >
        No chatbots or avatars available for this module
      </div>

      <!-- 聊天机器人卡片网格 -->
      <div class="row q-col-gutter-md">
        <div
          v-for="item in getValidChatbotsAndAvatars(module)"
          :key="item.type === 'chatbot' ? item.chatbot_id : item.avatar_id"
          class="col-12 col-sm-6 col-md-4"
        >
          <q-card flat bordered class="h-100 column course-card">
            <q-card-section>
              <div class="text-h6 q-mb-md">
                {{ item.type === 'chatbot' ? item.chatbot_name : item.avatar_name }}
              </div>
              <div class="q-gutter-y-sm text-body2">
                <div class="row items-center">
                  <q-icon name="school" size="xs" class="q-mr-sm" />
                  <span>Course: {{ courseTitle }}</span>
                </div>
                <div class="row items-center">
                  <q-icon name="person" size="xs" class="q-mr-sm" />
                  <span>Teacher: {{ item.creator_user_full_name }}</span>
                </div>
                <div class="row items-center">
                  <q-icon name="event" size="xs" class="q-mr-sm" />
                  <span>Created on: {{ item.created_at }}</span>
                </div>
              </div>
            </q-card-section>

            <q-space />
            <q-separator />

            <q-card-actions align="center" class="bytewise-background-blue">
              <q-btn
                round
                color="primary"
                icon="chat"
                size="sm"
                @click="
                  continuePreviousChat(item.chatbot_id, module.module_id, module.module_title)
                "
                v-if="item.type === 'chatbot'"
              >
                <q-tooltip>Start to Chat</q-tooltip>
              </q-btn>
              <q-btn
                round
                color="primary"
                icon="chat"
                size="sm"
                v-if="item.type === 'avatar'"
                @click="jumpToAvatarChat(item.avatar_id, module.module_id)"
              >
                <q-tooltip>Start to Talk</q-tooltip>
              </q-btn>
            </q-card-actions>
          </q-card>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { api } from 'boot/axios';
import { onMounted } from 'vue';

const route = useRoute();
const router = useRouter();

interface ChatbotItem {
  chatbot_id: string;
  chatbot_name: string;
  created_at: string;
  creator_user_id: string;
  creator_user_full_name: string;
}

interface ModuleChatbotItem {
  module_id: string;
  course_id: string;
  module_title: string;
  description: JSON;
  created_at: string;
  chatbots: ChatbotItem[];
}
interface AvatarItem {
  avatar_id: string;
  id: string;
  avatar_name: string;
  created_at: string;
  creator_user_id: string;
  creator_user_full_name: string;
}

interface ModuleCombinedItem {
  module_id: string;
  course_id: string;
  module_title: string;
  description: JSON;
  created_at: string;
  chatbots: ChatbotItem[];
  avatars: AvatarItem[];
}

interface ModuleAvatarItem {
  module_id: string;
  course_id: string;
  module_title: string;
  description: JSON;
  created_at: string;
  avatars: AvatarItem[];
}

const getStringParam = (param: string | string[]): string => {
  return Array.isArray(param) ? param[0] || '' : param;
};

const moduleChatbotList = ref<ModuleChatbotItem[]>([]);
const moduleAvatarList = ref<ModuleAvatarItem[]>([]);
const moduleCombinedList = ref<ModuleCombinedItem[]>([]);

const isFetching = ref<boolean>(true);
const courseId = ref<string>(getStringParam(route.params.courseId || ''));
const courseTitle = ref<string>('');

const continuePreviousChat = (chatbot_id: string, module_id: string, module_title: string) => {
  void router.push({
    path: `/chat/${chatbot_id}/session/latest`,
    query: {
      courseId: courseId.value,
      courseTitle: courseTitle.value,
      moduleId: module_id,
      moduleTitle: module_title,
    },
  });
};

const utcTimeToLocalTime = (utcTime: string): string => {
  return new Date(utcTime).toLocaleString();
};

const jumpToAvatarChat = async (avatar_id: string, module_id: string) => {
  await router.push('/avatar?id=' + avatar_id + '&module_id=' + module_id);
};

type TaggedChatbot = ChatbotItem & { type: 'chatbot' };
type TaggedAvatar = AvatarItem & { type: 'avatar' };
type CombinedItem = TaggedChatbot | TaggedAvatar;

// 添加一个函数来过滤有效的聊天机器人
const getValidChatbotsAndAvatars = (module: ModuleCombinedItem): CombinedItem[] => {
  const validChatbots = module.chatbots
    .filter((chatbot) => chatbot.chatbot_id)
    .map((chatbot) => ({ ...chatbot, type: 'chatbot' as const }));
  const validAvatars = module.avatars
    .filter((avatar) => avatar.avatar_id)
    .map((avatar) => ({ ...avatar, type: 'avatar' as const }));
  return [...validChatbots, ...validAvatars];
};

const fetchCourseInfo = async () => {
  const response = await api.get('/course-info', {
    params: {
      course_id: courseId.value,
    },
  });
  courseTitle.value = response.data.course_title;
};

const fetchModuleChatbotList = async () => {
  const response = await api.get('/module-chatbot-list', {
    params: {
      course_id: courseId.value,
    },
  });
  moduleChatbotList.value = response.data;

  // Sort the Modules and Chatbots by created_at from small to large
  moduleChatbotList.value.sort(
    (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
  );
  moduleChatbotList.value.forEach((module) => {
    module.chatbots.sort(
      (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
    );
  });

  // Convert UTC time to local time
  moduleChatbotList.value.forEach((module) => {
    module.chatbots.forEach((chatbot) => {
      chatbot.created_at = utcTimeToLocalTime(chatbot.created_at);
    });
  });
};

function combineModuleLists(
  moduleChatbotList: ModuleChatbotItem[],
  moduleAvatarList: ModuleAvatarItem[],
) {
  // Step 1: Create a Map for fast lookup of avatars by module_id
  const avatarMap = new Map();
  moduleAvatarList.forEach((module) => {
    avatarMap.set(module.module_id, module);
  });

  // Step 2: Process each module in moduleChatbotList
  const result = moduleChatbotList.map((module) => {
    const avatarModule = avatarMap.get(module.module_id);
    return {
      module_id: module.module_id ?? null,
      course_id: module.course_id ?? null,
      module_title: module.module_title ?? null,
      description: module.description ?? null,
      created_at: module.created_at ?? null,
      chatbots:
        Array.isArray(module.chatbots) && module.chatbots.length > 0
          ? module.chatbots.map((chatbot) => ({
              chatbot_id: chatbot.chatbot_id ?? null,
              chatbot_name: chatbot.chatbot_name ?? null,
              created_at: chatbot.created_at ?? null,
              creator_user_id: chatbot.creator_user_id ?? null,
              creator_user_full_name: chatbot.creator_user_full_name ?? null,
            }))
          : [],
      avatars:
        avatarModule && Array.isArray(avatarModule.avatars) && avatarModule.avatars.length > 0
          ? avatarModule.avatars.map((avatar: AvatarItem) => ({
              avatar_id: avatar.avatar_id ?? null,
              avatar_name: avatar.avatar_name ?? null,
              created_at: avatar.created_at ?? null,
              creator_user_id: avatar.creator_user_id ?? null,
              creator_user_full_name: avatar.creator_user_full_name ?? null,
            }))
          : [],
    };
  });

  // Step 3: Add modules that are only in moduleAvatarList
  moduleAvatarList.forEach((module) => {
    if (!moduleChatbotList.some((m) => m.module_id === module.module_id)) {
      result.push({
        module_id: module.module_id ?? null,
        course_id: module.course_id ?? null,
        module_title: '',
        description: {} as JSON,
        created_at: '',
        chatbots: [],
        avatars:
          Array.isArray(module.avatars) && module.avatars.length > 0
            ? module.avatars.map((avatar) => ({
                avatar_id: avatar.avatar_id ?? null,
                avatar_name: avatar.avatar_name ?? null,
                created_at: avatar.created_at ?? null,
                creator_user_id: avatar.creator_user_id ?? null,
                creator_user_full_name: avatar.creator_user_full_name ?? null,
              }))
            : [],
      });
    }
  });

  return result;
}

const fetchModuleAvatarList = async () => {
  // Fetch course modules and chatbots from the server
  const response = await api.get('/module-avatar-list', {
    params: {
      course_id: courseId.value,
    },
  });
  // Update the moduleChatbotList with the fetched data
  moduleAvatarList.value = response.data;

  moduleAvatarList.value.sort(
    (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
  );
  moduleAvatarList.value.forEach((module) => {
    module.avatars.sort(
      (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
    );
  });
  // Convert UTC time to local time
  moduleAvatarList.value.forEach((module) => {
    module.avatars.forEach((avatar) => {
      avatar.created_at = utcTimeToLocalTime(avatar.created_at);
    });
  });
};

watch(route, async (newRoute) => {
  isFetching.value = true;
  courseId.value = getStringParam(newRoute.params.courseId || '');

  await fetchCourseInfo();
  await Promise.all([fetchModuleChatbotList(), fetchModuleAvatarList()]);
  moduleCombinedList.value = combineModuleLists(moduleChatbotList.value, moduleAvatarList.value);
  isFetching.value = false;
});

onMounted(async () => {
  isFetching.value = true;
  await fetchCourseInfo();
  await Promise.all([fetchModuleChatbotList(), fetchModuleAvatarList()]);
  moduleCombinedList.value = combineModuleLists(moduleChatbotList.value, moduleAvatarList.value);
  isFetching.value = false;
});
</script>

<style scoped lang="scss">
@import 'src/css/app.scss';
.h-100 {
  height: 100%;
}

.course-card:hover {
  transform: translateY(-1px);
  transition: transform 0.2s ease, 0.2s ease;
}
</style>
