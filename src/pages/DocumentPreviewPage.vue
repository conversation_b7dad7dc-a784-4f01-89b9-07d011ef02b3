<template>
  <q-layout view="hHh LpR lfr" class="fullscreen-layout">
    <q-page-container>
      <DocumentPreview
        :html-content="previewState.htmlContent"
        mode="fullscreen"
        :visible="true"
        :resizable="false"
        :title="previewState.title"
        @close="handleClose"
        @exit-fullscreen="handleExit"
      />
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import DocumentPreview from '../components/DocumentPreview.vue'
import { previewState } from '../utils/documentPreview'

const route = useRoute()
const router = useRouter()

onMounted(() => {
  // Check if we have content in the global state
  if (!previewState.htmlContent) {
    // Fallback: try to get content from query params (legacy support)
    const content = route.query.content as string
    if (content) {
      try {
        previewState.htmlContent = decodeURIComponent(content)
        previewState.title = 'Document Preview'
        previewState.isVisible = true
      } catch (err) {
        console.error('Failed to decode content:', err)
        previewState.htmlContent = content
      }
    }
  }
  
  // If still no content, redirect back
  if (!previewState.htmlContent) {
    router.go(-1)
  }
})

const handleClose = () => {
  router.go(-1)
}

const handleExit = () => {
  router.go(-1)
}
</script>

<style scoped lang="scss">
.fullscreen-layout {
  height: 100vh;
  width: 100vw;
}

.fullscreen-layout .q-page-container {
  padding: 0;
  height: 100vh;
  overflow: hidden;
}
</style> 