<template>
  <q-layout>
    <q-header class="bg-white text-dark">
      <q-toolbar>
        <q-toolbar-title>
          <q-btn
            flat
            no-caps
            label="Bytewise"
            to="/start"
            class="text-weight-bold"
            style="font-size: 1.3125rem; padding: 0 10px"
          />
        </q-toolbar-title>
        <q-space />

        <q-btn flat no-caps label="Login" to="login" />
      </q-toolbar>
    </q-header>
    <q-page-container class="bytewise-background-blue">
      <q-page class="row items-center justify-evenly q-pa-md ">
        <!-- <q-img src="https://picsum.photos/400" class="col-5" /> -->

        <!-- <div class="col-4"> -->
        <div class="reg-page-form">
          <div class="text-h4 text-weight-bold text-start q-mb-lg">Join Bytewise for free</div>
          <div class="text-h6 text-weight-light text-start q-mb-lg">
            Unlock a new experience of chatbot customisation <br />
            <!-- Flexible plans -->
          </div>
          <q-input
            v-model="form.fullName"
            label="Your full name"
            clearable
            :rules="[(val) => !!val || 'Full name is required']"
          />

          <q-input
            v-model="form.email"
            label="Your email"
            clearable
            type="email"
            :rules="[
              (val) => !!val || 'Email is required',
              (val) => /.+@.+\..+/.test(val) || 'Email must be valid',
            ]"
          />

          <q-input
            v-model="form.password"
            label="Create a password"
            clearable
            type="password"
            :rules="[
              (val) => !!val || 'Password is required',
              (val) => val.length >= 6 || 'Password must be at least 6 characters',
            ]"
          />

          <q-input
            v-model="form.passwordRepeat"
            label="Repeat password"
            clearable
            type="password"
            :rules="[(val) => val === form.password || 'Passwords must match']"
          />

          I am a:
          <q-radio v-model="form.role" label="Teacher" val="Teacher" />
          <q-radio v-model="form.role" label="Student" val="Student" />

          <div>If you don't have or prefer not to share your staff/student ID, please enter 0:</div>
          <q-input
            v-model="form.personnelId"
            label="Staff ID or Student ID"
            clearable
            :rules="[(val) => !!val || 'Staff ID or Student ID is required']"
            @keyup.enter="onRegister"
          />

          <q-checkbox v-model="form.agreeToTerms">
            I agree to the
            <a href="/#/about" target="_blank" class="text-primary" @click.stop
              >Terms and Conditions
            </a>
          </q-checkbox>

          <q-btn
            class="q-mt-md"
            label="Get started"
            @click="onRegister"
            color="black"
            text-color="white"
            style="width: 98%"
          />
          <!-- <q-btn class="q-mt-md " flat label="or connect with" style="width: 98%;color: #000000" />
          <q-btn class="q-mt-md" label="Sign in" @click="navigateToStu" color="black" text-color="white"
            style="width: 98%" /> -->
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { onMounted, ref } from 'vue';
import { apiRegister } from 'src/api/register';
import { onNotify } from 'src/utils/notifications';

const router = useRouter();
const $q = useQuasar();

const form = ref({
  fullName: '',
  email: '',
  password: '',
  passwordRepeat: '',
  role: '',
  personnelId: '',
  agreeToTerms: true,
});

const onRegister = async () => {
  onNotify($q);

  if (!form.value.agreeToTerms) {
    $q.notify({
      type: 'negative',
      message: 'Please agree to the Terms and Conditions',
    });
    return;
  }

  if (!form.value.fullName) {
    $q.notify({
      type: 'negative',
      message: 'Full name is required',
    });
    return;
  }

  if (!form.value.email) {
    $q.notify({
      type: 'negative',
      message: 'Email is required',
    });
    return;
  }

  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailPattern.test(form.value.email)) {
    $q.notify({
      type: 'negative',
      message: 'Email format is invalid',
    });
    return;
  }

  if (!form.value.password) {
    $q.notify({
      type: 'negative',
      message: 'Password is required',
    });
    return;
  }

  if (form.value.password.length < 6) {
    $q.notify({
      type: 'negative',
      message: 'Password must be at least 6 characters long',
    });
    return;
  }

  if (form.value.password !== form.value.passwordRepeat) {
    $q.notify({
      type: 'negative',
      message: 'Passwords do not match',
    });
    return;
  }

  if (!form.value.role) {
    $q.notify({
      type: 'negative',
      message: 'Role is required',
    });
    return;
  }

  if (!form.value.personnelId) {
    $q.notify({
      type: 'negative',
      message: 'Staff ID or Student ID is required',
    });
    return;
  }
  try {
    // Call the API to register the user
    await apiRegister({
      fullName: form.value.fullName,
      email: form.value.email,
      password: form.value.password,
      role: form.value.role,
      personnelId: form.value.personnelId,
    });
    // After successful registration, redirect to the login page
    $q.notify({
      type: 'positive',
      message:
        'Registration successful, we have sent you an email to activate your account. Only activated accounts can login.',
    });
    await router.push('/login');
  } catch {
    $q.notify({
      type: 'negative',
      message:
        'Registration failed. The email you entered may already be registered, please login.',
    });
  }
};

onMounted(() => {
  onNotify($q);
});

defineOptions({
  name: 'RegisterPage',
});
</script>

<style lang="scss" scoped>
@import 'src/css/app.scss';
.reg-page-form {
  width: 95%;
  max-width: 800px; // 限制最大宽度，避免超宽屏幕过散
  min-width: 300px; // 确保小屏幕有最小宽度
  background-color: map-get($white-colors, white);
  padding: 20px clamp(20px, 10vw, 80px); // 动态调整左右内边距
  text-align: center;
  border-radius: 10px;
  box-shadow: 0 2px 4px map-get($white-colors, shadow-light);
  margin: 0 auto; // 水平居中

  // 小屏幕进一步优化
  @media (max-width: 600px) {
    padding: 15px clamp(15px, 5vw, 30px); // 小屏幕减小内边距
  }

  // 暗模式适配
  @media (prefers-color-scheme: dark) {
    background-color: map-get($white-colors, bg-dark);
    box-shadow: 0 2px 4px map-get($white-colors, shadow-dark);
  }
}
</style>
