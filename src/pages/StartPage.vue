<template>
  <q-layout>
    <q-page-container class="bytewise-background-blue">
      <q-page class="row items-center justify-evenly q-pa-md">
        <div class="q-gutter-md">
          <div class="text-h2 text-weight-bold text-center q-mb-lg">
            {{ t('start_page_title') }}
          </div>
          <q-banner class="q-mt-md bytewise-background-blue" inline-actions dense>
            <div class="text-h6 text-center">
              {{ t('start_page_slogan') }}
            </div>
          </q-banner>
          <q-btn
            class="q-mt-md full-width"
            :label="t('start_page_get_started_btn')"
            to="register"
            color="black"
            text-color="white"
          />

          <q-btn
            class="q-mt-md full-width"
            :label="t('start_page_login_btn')"
            to="login"
            color="black"
            text-color="white"
          />
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import { onNotify } from 'src/utils/notifications';

const $q = useQuasar();
const { t } = useI18n();

defineOptions({
  name: 'StartPage',
});

onMounted(() => {
  onNotify($q);
});
</script>

<style lang="scss" scoped>
@import 'src/css/app.scss';
</style>
