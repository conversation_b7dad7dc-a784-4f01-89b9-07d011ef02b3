<template>
  <q-page v-if="!isFetching" class="q-pa-md " >
    <div class="text-h3 q-pa-md">Notifications</div>

    <q-separator spaced />
    <!-- Internal Message Notifications (Raise Hand) for Teachers -->
    <div v-if="isTeacher && internalMessageNotifications.length > 0" class="q-pa-md">
      <div class="text-h5 q-mb-md text-orange-8">
        <q-icon name="pan_tool" class="q-mr-sm" />
        Students Raising Hand
      </div>
      <div class="row q-col-gutter-md">
        <div
          v-for="notification in internalMessageNotifications"
          :key="notification.notification_id"
          class="col-12 col-sm-6 col-md-4"
        >
          <q-card flat bordered class="h-100 bg-orange-1 border-left-orange notification-card">
            <q-card-section>
              <div class="text-h6 text-orange-8 q-mb-md">
                <q-icon name="pan_tool" class="q-mr-sm" />
                Student needs help
              </div>
              <div class="q-gutter-y-xs text-body2">
                <div class="row items-center">
                  <q-icon name="school" size="xs" class="q-mr-sm" />
                  <span><strong>Course:</strong> {{ notification.course_title }}</span>
                </div>
                <div class="row items-center">
                  <q-icon name="class" size="xs" class="q-mr-sm" />
                  <span><strong>Module:</strong> {{ notification.description.module_title }}</span>
                </div>
                <div class="row items-center">
                  <q-icon name="person" size="xs" class="q-mr-sm" />
                  <span><strong>Student:</strong> {{ notification.description.student_name }}</span>
                </div>
                <div class="row items-center">
                  <q-icon name="chat" size="xs" class="q-mr-sm" />
                  <span><strong>Session:</strong> {{ notification.description.session_name }}</span>
                </div>
                <div class="row items-center">
                  <q-icon name="message" size="xs" class="q-mr-sm" />
                  <span
                    ><strong>Message:</strong> {{ notification.description.message_content }}</span
                  >
                </div>
                <div class="row items-center">
                  <q-icon name="event" size="xs" class="q-mr-sm" />
                  <span class="text-caption text-grey-6">{{ notification.created_at }}</span>
                </div>
              </div>
            </q-card-section>

            <q-separator />

            <q-card-actions align="center" class="bytewise-background-blue">
              <q-btn
                round
                color="primary"
                icon="open_in_new"
                size="sm"
                @click="goToInternalMessageSession(notification)"
              >
                <q-tooltip>Go to Session</q-tooltip>
              </q-btn>
              <q-btn
                round
                color="grey"
                icon="close"
                size="sm"
                @click="dismissInternalMessageNotification(notification)"
              >
                <q-tooltip>Dismiss</q-tooltip>
              </q-btn>
            </q-card-actions>
          </q-card>
        </div>
      </div>
    </div>

    <!-- Teacher Reply Notifications for Students -->
    <div v-if="!isTeacher && teacherReplyNotifications.length > 0" class="q-pa-md">
      <div class="text-h5 q-mb-md text-blue-8">
        <q-icon name="reply" class="q-mr-sm" />
        Teacher Replies
      </div>
      <div class="row q-col-gutter-md">
        <div
          v-for="notification in teacherReplyNotifications"
          :key="notification.notification_id"
          class="col-12 col-sm-6 col-md-4"
        >
          <q-card flat bordered class="h-100 bg-blue-1 border-left-blue notification-card">
            <q-card-section>
              <div class="text-h6 text-blue-8 q-mb-md">
                <q-icon name="reply" class="q-mr-sm" />
                Teacher replied to your question
              </div>
              <div class="q-gutter-y-xs text-body2">
                <div class="row items-center">
                  <q-icon name="school" size="xs" class="q-mr-sm" />
                  <span><strong>Course:</strong> {{ notification.course_title }}</span>
                </div>
                <div class="row items-center">
                  <q-icon name="class" size="xs" class="q-mr-sm" />
                  <span><strong>Module:</strong> {{ notification.description.module_title }}</span>
                </div>
                <div class="row items-center">
                  <q-icon name="person" size="xs" class="q-mr-sm" />
                  <span><strong>Teacher:</strong> {{ notification.description.teacher_name }}</span>
                </div>
                <div class="row items-center">
                  <q-icon name="chat" size="xs" class="q-mr-sm" />
                  <span><strong>Session:</strong> {{ notification.description.session_name }}</span>
                </div>
                <div class="row items-center">
                  <q-icon name="reply" size="xs" class="q-mr-sm" />
                  <span><strong>Reply:</strong> {{ notification.description.reply_content }}</span>
                </div>
                <div class="row items-center">
                  <q-icon name="event" size="xs" class="q-mr-sm" />
                  <span class="text-caption text-grey-6">{{ notification.created_at }}</span>
                </div>
              </div>
            </q-card-section>

            <q-separator />

            <q-card-actions align="center" class="bytewise-background-blue">
              <q-btn
                round
                color="primary"
                icon="visibility"
                size="sm"
                @click="goToTeacherReplySession(notification)"
              >
                <q-tooltip>View Session</q-tooltip>
              </q-btn>
              <q-btn
                round
                color="grey"
                icon="close"
                size="sm"
                @click="dismissTeacherReplyNotification(notification)"
              >
                <q-tooltip>Dismiss</q-tooltip>
              </q-btn>
            </q-card-actions>
          </q-card>
        </div>
      </div>
    </div>

    <!-- Regular Notifications -->
    <div class="q-gutter-sm q-pa-md" v-if="isTeacher">
      <q-btn round color="primary" icon="add" size="md" @click="createNewNotification()">
        <q-tooltip>Create New Notification</q-tooltip>
      </q-btn>
    </div>
    <div
      v-if="
        notificationList.length === 0 &&
        (!isTeacher || internalMessageNotifications.length === 0) &&
        (isTeacher || teacherReplyNotifications.length === 0)
      "
      class="text-h6 q-pa-md"
    >
      No notifications available
    </div>
    <div v-if="notificationList.length > 0" class="row q-col-gutter-md q-pa-md">
      <div
        v-for="notification in notificationList"
        :key="notification.notification_id"
        class="col-12 col-sm-6 col-md-4"
      >
        <q-card flat bordered class="h-100 notification-card">
          <q-card-section >
            <div class="text-h6 q-mb-md">{{ notification.notification_title }}</div>
            <div class="q-gutter-y-xs text-body2">
              <div class="row items-center">
                <q-icon name="school" size="xs" class="q-mr-sm" />
                <span><strong>Course:</strong> {{ notification.course_title }}</span>
              </div>
              <div class="row items-center">
                <q-icon name="person" size="xs" class="q-mr-sm" />
                <span><strong>Teacher:</strong> {{ notification.creator_user_full_name }}</span>
              </div>
              <div class="row items-center">
                <q-icon name="event" size="xs" class="q-mr-sm" />
                <span><strong>Published on:</strong> {{ notification.created_at }}</span>
              </div>
              <div class="row items-start">
                <q-icon name="description" size="xs" class="q-mr-sm q-mt-xs" />
                <span><strong>Content:</strong> {{ notification.description.content }}</span>
              </div>
            </div>
          </q-card-section>

          <q-separator v-if="isTeacher" />

          <q-card-actions align="center" v-if="isTeacher" class="bytewise-background-blue">
            <q-btn
              round
              color="negative"
              icon="delete"
              size="sm"
              @click="deleteCurrentNotification(notification)"
            >
              <q-tooltip>Delete this Notification</q-tooltip>
            </q-btn>
          </q-card-actions>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { api } from 'boot/axios';
import { onMounted } from 'vue';

const route = useRoute();
const router = useRouter();
const $q = useQuasar();

const isTeacher = ref<boolean>(false);

interface NotificationDescriptionItem {
  content: string;
}

interface NotificationItem {
  student_id: string;
  group_id: string;
  course_id: string;
  course_title: string;
  creator_user_id: string;
  creator_user_full_name: string;
  notification_id: string;
  notification_title: string;
  description: NotificationDescriptionItem;
  created_at: string;
  updated_at: string;
  deleted_at: string;
}

interface InternalMessageNotificationItem {
  notification_id: string; // uuid, PK - 通知识别码
  course_id: string; // uuid, FK - 课程识别码
  creator_user_id: string; // uuid, FK - 通知创建用户识别码
  notification_title: string; // varchar - 通知标题
  description: {
    // json - 通知详情 (for raise hand notifications)
    type: 'raise_hand';
    session_id: string;
    session_name: string;
    module_id: string;
    module_title: string;
    chatbot_id: string;
    student_user_id: string;
    student_name: string;
    message_content: string; // First message content preview
  };

  // Additional fields for frontend display (derived from joins)
  course_title?: string; // Derived from courses table
  created_at?: string; // System timestamp
}

interface TeacherReplyNotificationItem {
  notification_id: string; // uuid, PK - 通知识别码
  course_id: string; // uuid, FK - 课程识别码
  creator_user_id: string; // uuid, FK - student user_id (for notifications table)
  notification_title: string; // varchar - 通知标题
  description: {
    // json - 通知详情 (for teacher reply notifications)
    type: 'teacher_reply';
    session_id: string;
    session_name: string;
    module_id: string;
    module_title: string;
    chatbot_id: string;
    course_id: string;
    course_title: string;
    teacher_user_id: string;
    teacher_name: string;
    reply_content: string; // Teacher's reply content preview
  };

  // Additional fields for frontend display (derived from joins)
  course_title?: string; // Derived from courses table or description
  created_at?: string; // System timestamp
}

const notificationList = ref<NotificationItem[]>([]);
const internalMessageNotifications = ref<InternalMessageNotificationItem[]>([]);
const teacherReplyNotifications = ref<TeacherReplyNotificationItem[]>([]);

const isFetching = ref<boolean>(true);

const createNewNotification = async () => {
  // Redirect to the create new notification page
  await router.push('/teacher/notification/new');
};

const deleteCurrentNotification = (notification: NotificationItem) => {
  try {
    $q.dialog({
      title: 'Delete Notification',
      message:
        'Are you sure you want to delete this notification (' +
        notification.notification_title +
        ')?',
      ok: 'Yes',
      cancel: 'No',
    }).onOk(() => {
      void (async () => {
        // Delete this notification
        await api.delete('/notification', {
          params: {
            notification_id: notification.notification_id,
          },
        });
        // Notify the user about the successful deletion
        $q.notify({
          type: 'positive',
          message: 'Notification (' + notification.notification_title + ') deleted successfully',
        });
        // Fetch the updated notification list
        await fetchNotificationList();
      })();
    });
  } catch (error) {
    // Notify the user about the error
    $q.notify({
      type: 'negative',
      message: 'Failed to delete the notification: ' + String(error),
    });
  }
};

const utcTimeToLocalTime = (utcTime: string): string => {
  return new Date(utcTime).toLocaleString();
};

const fetchNotificationList = async () => {
  // Fetch notification list from the server (regular notifications only)
  const response = await api.get('/notification-list');
  // Filter out teacher_reply notifications as they should be handled separately
  const filteredNotifications = response.data.filter(
    (item: NotificationItem | TeacherReplyNotificationItem) => {
      const description = item.description || {};
      const descType = (description as { type?: string }).type;
      return !descType || (descType !== 'teacher_reply' && descType !== 'raise_hand');
    },
  );

  // Remove duplicates based on notification_id
  const uniqueNotifications = Array.from(
    new Map(
      filteredNotifications.map((item: NotificationItem) => [item.notification_id, item]),
    ).values(),
  ) as NotificationItem[];

  // Update the notificationList with the unique notifications
  notificationList.value = uniqueNotifications;

  // Sort the Notifications by created_at from newest to oldest
  notificationList.value.sort(
    (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
  );

  // Convert UTC time to local time
  notificationList.value.forEach((notification) => {
    notification.created_at = utcTimeToLocalTime(notification.created_at);
  });
};

const fetchInternalMessageNotifications = async () => {
  if (!isTeacher.value) return;

  try {
    // Fetch notifications filtered by type 'raise_hand'
    const response = await api.get('/notification-list', {
      params: {
        notification_type: 'raise_hand',
      },
    });
    internalMessageNotifications.value = response.data;

    // Sort the notifications by created_at from newest to oldest
    internalMessageNotifications.value.sort(
      (a, b) => new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime(),
    );

    // Convert UTC time to local time
    internalMessageNotifications.value.forEach((notification) => {
      if (notification.created_at) {
        notification.created_at = utcTimeToLocalTime(notification.created_at);
      }
    });
  } catch (error) {
    console.error('Error fetching internal message notifications:', error);
  }
};

const fetchTeacherReplyNotifications = async () => {
  if (isTeacher.value) return; // Only fetch for students

  try {
    // Fetch teacher reply notifications using the correct dedicated endpoint
    const response = await api.get('/teacher-reply-notifications');

    teacherReplyNotifications.value = response.data;

    // Sort the notifications by created_at from newest to oldest
    teacherReplyNotifications.value.sort(
      (a, b) => new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime(),
    );

    // Convert UTC time to local time
    teacherReplyNotifications.value.forEach((notification) => {
      if (notification.created_at) {
        notification.created_at = utcTimeToLocalTime(notification.created_at);
      }
    });
  } catch (error) {
    console.error('Error fetching teacher reply notifications:', error);
  }
};

const goToInternalMessageSession = async (notification: InternalMessageNotificationItem) => {
  // Navigate to the student's chat session in ChatbotUsageLayout
  await router.push({
    path: `/teacher/chatbot/${notification.description.chatbot_id}/usage/${notification.description.student_user_id}/session/${notification.description.session_id}`,
    query: {
      sessionName: notification.description.session_name,
      chatbotName: '', // Will be fetched in the layout
      courseId: notification.course_id,
      courseTitle: notification.course_title || '',
      moduleId: notification.description.module_id,
      moduleTitle: notification.description.module_title,
    },
  });
};

const goToTeacherReplySession = async (notification: TeacherReplyNotificationItem) => {
  // Navigate to the student's own chat session where the teacher replied
  await router.push({
    path: `/chat/${notification.description.chatbot_id}/session/${notification.description.session_id}`,
    query: {
      sessionName: notification.description.session_name,
      courseId: notification.description.course_id,
      courseTitle: notification.course_title || notification.description.course_title || '',
      moduleId: notification.description.module_id,
      moduleTitle: notification.description.module_title,
    },
  });
};

const dismissInternalMessageNotification = async (
  notification: InternalMessageNotificationItem,
) => {
  try {
    // Delete notification using REST API endpoint
    await api.delete('/notification', {
      params: {
        notification_id: notification.notification_id,
      },
    });

    // Remove from local array
    internalMessageNotifications.value = internalMessageNotifications.value.filter(
      (item) => item.notification_id !== notification.notification_id,
    );

    $q.notify({
      type: 'positive',
      message: 'Notification dismissed',
    });
  } catch (error) {
    console.error('Error dismissing notification:', error);
    $q.notify({
      type: 'negative',
      message: 'Failed to dismiss notification',
    });
  }
};

const dismissTeacherReplyNotification = async (notification: TeacherReplyNotificationItem) => {
  try {
    // Delete notification using the general notification API endpoint
    await api.delete('/notification', {
      params: {
        notification_id: notification.notification_id,
      },
    });

    // Remove from local array
    teacherReplyNotifications.value = teacherReplyNotifications.value.filter(
      (item) => item.notification_id !== notification.notification_id,
    );

    $q.notify({
      type: 'positive',
      message: 'Notification dismissed',
    });
  } catch (error) {
    console.error('Error dismissing notification:', error);
    $q.notify({
      type: 'negative',
      message: 'Failed to dismiss notification',
    });
  }
};

onMounted(async () => {
  isFetching.value = true;
  // Check if the user is a teacher
  if (route.path.includes('teacher')) {
    isTeacher.value = true;
  } else {
    isTeacher.value = false;
  }
  // Fetch notification list and internal message notifications
  await Promise.all([
    fetchNotificationList(),
    fetchInternalMessageNotifications(),
    fetchTeacherReplyNotifications(),
  ]);
  isFetching.value = false;
});
</script>

<style scoped lang="scss">
@import 'src/css/app.scss';
.border-left-orange {
  border-left: 4px solid #f57c00;
}

.border-left-blue {
  border-left: 4px solid #007bff;
}

.h-100 {
  height: 100%;
}

.notification-card:hover {
  transform: translateY(-2.5px);
  transition: transform 0.2s ease, 0.2s ease;
}
</style>
