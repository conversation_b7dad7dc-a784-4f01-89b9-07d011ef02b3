<template>
  <q-layout view="hHh lpR fFf">
    <!-- Top Bar -->
    <q-header elevated class="bg-white text-dark">
      <q-toolbar>
        <q-toolbar-title class="text-weight-bold">
          <q-btn
            flat
            no-caps
            label="Bytewise"
            to="/teacher/homepage"
            class="text-weight-bold"
            style="font-size: 1.3125rem; padding: 5px"
          />
        </q-toolbar-title>
      </q-toolbar>
    </q-header>
    <q-page-container>
      <q-page v-if="!isFetching">
        <div class="q-banner text-h6 q-pa-md row q-gutter-sm">
          <q-btn flat dense round icon="arrow_back" :to="urlToCourse(courseId)">
            <q-tooltip>Back to Course</q-tooltip>
          </q-btn>
          <div>Manage Student Group - {{ studentGroupName }}</div>
        </div>
        <!-- <q-separator spaced /> -->

        <div class="q-pa-md bytewise-background-grey-100">
          <div class="text-h6">Add Student to the connected Student Group</div>
          <div>Note:</div>
          <div>
            1. The student must have an account in Bytewise, and the email must be the same as the
            student's email.
          </div>
          <div>
            2. The students added here will affect all courses connected with the current student
            group.
          </div>
          <q-card>
            <q-card-section>
              <q-input
                v-model="email"
                label="Enter student's email to add"
                clearable
                type="email"
                :rules="[
                  (val) => !!val || 'Email is required',
                  (val) => /.+@.+\..+/.test(val) || 'Email must be valid',
                ]"
                @keyup.enter="handleAddStudent"
              >
                <template v-slot: after>
                  <q-btn flat icon=" add" @click="handleAddStudent">
                    <q-tooltip>Add Student</q-tooltip>
                  </q-btn>
                </template>
              </q-input>
              <div class="text-h6">Or create an invitation link for students to join</div>
              <q-btn
                no-caps
                label="Create Invitation Link"
                color="black"
                text-color="white"
                @click="handleCreateInvitationLink"
              >
                <q-tooltip>Create an invitation link for students to join</q-tooltip>
              </q-btn>
            </q-card-section>
          </q-card>
        </div>
        <q-separator/>
        <div class="q-pa-md bytewise-background-grey-100">
          <div class="text-h6">Students in the connected Student Group</div>
          <q-table :rows-per-page-options="[10, 20]" :columns="columns" :rows="rows">
            <template v-slot:header="props">
              <q-tr :props="props">
                <q-th auto-width />
                <q-th v-for="col in props.cols" :key="col.name" :props="props">
                  {{ col.label }}
                </q-th>
              </q-tr>
            </template>
            <template v-slot:body="props">
              <q-tr :props="props">
                <q-td auto-width>
                  <q-btn flat dense round icon="delete" @click="handleDeleteStudent(props.row)" />
                </q-td>
                <q-td v-for="col in props.cols" :key="col.name" :props="props">
                  {{ col.value }}
                </q-td>
              </q-tr>
            </template>
          </q-table>
        </div>
        <q-separator/>
        <div class="q-pa-md bytewise-background-grey-100">
          <div class="text-h6">Want to change the connected Student Group?</div>
          <div>Note:</div>
          <div>
            1. You are able to change the student group for the current course. However, all
            students in the current student group will be affected.
          </div>
          <div>
            2. You can only enter the group_id in the textbox below to change the student group.
          </div>
          <q-card>
            <q-card-section>
              <q-input
                v-model="newStudentGroupId"
                label="Group ID to be changed"
                clearable
                type="text"
                :rules="[(val) => !!val || 'Group ID is required']"
              >
                <q-btn flat color="black" label="Submit" @click="handleChangeStudentGroup" />
              </q-input>
            </q-card-section>
          </q-card>
        </div>
        <q-separator/>
        <div class="q-pa-md bytewise-background-grey-100">
          <div class="text-h6">Group ID of the current connected Student Group</div>
          <div>Note: Please keep this group_id for future reference.</div>
          <q-card>
            <q-card-section>
              <q-input v-model="studentGroupId" label="Current Group ID" type="text" readonly />
            </q-card-section>
          </q-card>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useQuasar } from 'quasar';
import { api } from 'boot/axios';

const getStringParam = (param: string | string[]): string => {
  return Array.isArray(param) ? param[0] || '' : param;
};

const $q = useQuasar();
//   const router = useRouter();
const route = useRoute();

const isFetching = ref(true);

const courseId = ref<string>('');
const urlToCourse = (courseId: string) => `/teacher/course/${courseId}`;
const email = ref<string>('');
const studentGroupName = ref<string>('');
const studentGroupId = ref<string>('');
const newStudentGroupId = ref<string>('');

interface Row {
  personnel_id: string;
  email: string;
  full_name: string;
}

interface Column {
  name: string;
  label: string;
  align: 'left' | 'right' | 'center';
  field: string | ((row: Row) => number | string);
  sortable: boolean;
  required?: boolean;
}

const columns: Column[] = [
  {
    name: 'Index',
    required: true,
    label: 'Index',
    align: 'left',
    field: (row: Row) => rows.value.indexOf(row) + 1,
    sortable: true,
  },
  {
    name: 'personnel_id',
    label: 'Student ID',
    align: 'left',
    field: 'personnel_id',
    sortable: true,
  },
  {
    name: 'email',
    label: 'Email',
    align: 'left',
    field: 'email',
    sortable: true,
  },
  {
    name: 'full_name',
    label: 'Full Name',
    align: 'left',
    field: 'full_name',
    sortable: true,
  },
];

const rows = ref<Row[]>([
  {
    personnel_id: '1',
    email: '<EMAIL>',
    full_name: 'John Doe',
  },
  {
    personnel_id: '2',
    email: '<EMAIL>',
    full_name: 'Jane Doe',
  },
]);

const courseStudentList = ref([
  {
    group_course_id: '1',
    group_id: '1',
    coruse_id: '1',
    group_name: 'Group 1',
    student_id: '1',
    username: 'johndoe',
    full_name: 'John Doe',
    email: '<EMAIL>',
    personnel_id: '1',
  },
]);

const handleAddStudent = async () => {
  // Handle adding student to the course
  try {
    // Trim the email
    email.value = email.value.trim();
    // Validate the email
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailPattern.test(email.value)) {
      $q.notify({
        type: 'negative',
        message: 'Email format is invalid',
      });
      return;
    }
    // Add student to the course
    await api.post('/email-enrollment', {
      group_id: studentGroupId.value,
      email: email.value,
    });
    // console.log(response.data);
    $q.notify({
      type: 'positive',
      message: 'Student added successfully',
    });
    // Update the rows
    await fetchCourseStudentList(courseId.value);
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Failed to add student: ' + String(error),
    });
  }
};

const getInvitationLink = () => {
  // Get the invitation link for students to join
  return window.location.origin + '/#/student/enroll/' + studentGroupId.value;
};

const handleCreateInvitationLink = async () => {
  // Handle creating an invitation link for students to join
  // Copy the share link to the client's clipboard
  await navigator.clipboard.writeText(getInvitationLink());
  // Tell the user that the invitation link has been created and the link to access it
  $q.notify({
    type: 'positive',
    message: 'The invitation link has been copied to your clipboard.',
  });
  $q.dialog({
    title: 'Invitation Link Created',
    message: 'The invitation link has been copied to your clipboard: ',
    prompt: {
      model: getInvitationLink(),
      type: 'text',
      dense: true,
      readonly: true,
    },
    ok: 'OK',
  });
};

const handleDeleteStudent = (row: Row) => {
  // Handle deleting student from the course
  // Get the user_id from courseStudentList by row
  const studentId = courseStudentList.value.find(
    (student) => student.email === row.email,
  )?.student_id;
  try {
    $q.dialog({
      title: 'Delete Student',
      message: 'Are you sure you want to delete the student (' + row.full_name + ')?',
      ok: 'Yes',
      cancel: 'No',
    }).onOk(() => {
      void (async () => {
        // Delete student from the course
        await api.delete('/enrollment', {
          params: {
            group_id: studentGroupId.value,
            student_id: studentId,
          },
        });
        // console.log(response.data);
        $q.notify({
          type: 'positive',
          message: 'Student deleted successfully',
        });
        // Update the rows
        await fetchCourseStudentList(courseId.value);
      })();
    });
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Failed to delete student: ' + String(error),
    });
  }
};

const handleChangeStudentGroup = () => {
  // Handle changing the student group
  try {
    $q.dialog({
      title: 'Change Student Group',
      message: 'Are you sure you want to change the student group?',
      ok: 'Yes',
      cancel: 'No',
    }).onOk(() => {
      void (async () => {
        // Change the student group
        await api.put('/group-course', {
          group_course_id: courseStudentList.value[0]?.group_course_id,
          group_id: newStudentGroupId.value,
        });
        // console.log(response.data);
        $q.notify({
          type: 'positive',
          message: 'Student group changed successfully',
        });
        // Update the rows
        await fetchCourseStudentList(courseId.value);
      })();
    });
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Failed to change student group: ' + String(error),
    });
  }
};

const fetchCourseStudentList = async (courseId: string) => {
  // Fetch course student list from the server
  const response = await api.get('/course-student-list', {
    params: {
      course_id: courseId,
    },
  });
  // console.log(response.data);
  // Update the rows with the fetched data
  rows.value = response.data;
  courseStudentList.value = response.data;
};

const fetchGroupInfo = async (courseId: string) => {
  // Fetch group info from the server
  const response = await api.get('/group-by-course', {
    params: {
      course_id: courseId,
    },
  });
  // console.log(response.data);
  // Update the student group information
  studentGroupName.value = response.data.group_name;
  studentGroupId.value = response.data.group_id;
};

onMounted(async () => {
  isFetching.value = true;

  courseId.value = getStringParam(route.params.courseId || '');

  await fetchCourseStudentList(courseId.value);
  await fetchGroupInfo(courseId.value);
  isFetching.value = false;
});
</script>

<style scoped >
.q-drawer {
  width: 300px;
}
</style>
