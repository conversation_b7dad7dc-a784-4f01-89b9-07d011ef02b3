<template>
  <q-page class="q-pa-md" v-if="!isFetching">
    <div class="text-h3 q-pa-md">Manage Chatbots</div>
    <q-separator spaced />
    <div class="q-gutter-sm q-pa-md">
      <q-btn
        no-caps
        label="Create New Chatbot"
        color="black"
        text-color="white"
        @click="handleAddChatbot"
      />
      <div class="hint">
        <p>Hint:</p>
        <p>
          1. After creating new chatbots, you should create new courses and modules to import those
          chatbots.
        </p>
        <p>2. Then, you can click "Start to Chat" to talk to the chatbot you created.</p>
      </div>
    </div>
    <q-separator spaced />
    <div class="q-pa-md bytewise-table-style">
      <div class="text-h6">Chatbot List</div>
      <div class="q-py-md">
        <q-input
          v-model="filter"
          label="Enter chatbot information for filtering"
          clearable
          type="text"
        />
      </div>
      <q-table :rows-per-page-options="[10, 20]" :columns="columns" :rows="rows" :filter="filter">
        <template v-slot:header="props">
          <q-tr :props="props">
            <!-- <q-th auto-width /> -->
            <q-th auto-width />
            <q-th v-for="col in props.cols" :key="col.name" :props="props">
              {{ col.label }}
            </q-th>
          </q-tr>
        </template>
        <template v-slot:body="props">
          <q-tr :props="props">
            <!-- <q-td auto-width>
              <q-btn flat dense round icon="play_circle_filled" @click="handleChat(props.row)">
                <q-tooltip>Start to Chat</q-tooltip>
              </q-btn>
            </q-td> -->
            <q-td auto-width>
              <q-btn
                flat
                dense
                round
                icon="content_copy"
                @click="handleDuplicateChatbot(props.row)"
                class="q-mr-xs"
                :loading="duplicatingChatbot === props.row.chatbot_unique_name"
                :disable="duplicatingChatbot === props.row.chatbot_unique_name"
              >
                <q-tooltip>{{
                  duplicatingChatbot === props.row.chatbot_unique_name
                    ? 'Duplicating...'
                    : 'Duplicate Chatbot'
                }}</q-tooltip>
              </q-btn>
              <q-btn flat dense round icon="settings" @click="handleSettingChatbot(props.row)">
                <q-tooltip>Setting</q-tooltip>
              </q-btn>
            </q-td>
            <q-td v-for="col in props.cols" :key="col.name" :props="props">
              {{ col.value }}
            </q-td>
          </q-tr>
        </template>
      </q-table>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { api } from 'boot/axios';

const $q = useQuasar();
const router = useRouter();
// const route = useRoute();

const isFetching = ref(true);
const duplicatingChatbot = ref<string | null>(null);

const filter = ref<string>('');

interface Row {
  chatbot_name: string;
  chatbot_unique_name: string;
  model_name: string;
  system_prompt: string;
  welcome_prompt: string;
  temperature: string;
  type_name: string;
  knowledge_base_file_names: string[];
  description?: Record<string, unknown>;
}

interface Column {
  name: string;
  label: string;
  align: 'left' | 'center' | 'right';
  field: string | ((row: Row) => number | string);
  sortable?: boolean;
  required?: boolean;
}

const columns: Column[] = [
  {
    name: 'Index',
    required: true,
    label: 'Index',
    align: 'left',
    field: (row: Row) => rows.value.indexOf(row) + 1,
    sortable: true,
  },
  {
    name: 'chatbot_name',
    label: 'Chatbot Name',
    align: 'left',
    field: 'chatbot_name',
    sortable: true,
  },
  {
    name: 'model_name',
    label: 'Model Name',
    align: 'left',
    field: 'model_name',
  },
  {
    name: 'system_prompt',
    label: 'System Prompt',
    align: 'left',
    field: 'system_prompt',
  },
  {
    name: 'welcome_prompt',
    label: 'Welcome Prompt',
    align: 'left',
    field: 'welcome_prompt',
  },
  {
    name: 'temperature',
    label: 'Temperature',
    align: 'left',
    field: 'temperature',
  },
  {
    name: 'type_name',
    label: 'Type Name',
    align: 'left',
    field: 'type_name',
  },
  {
    name: 'knowledge_base_file_names',
    label: 'Knowledge Base File Names',
    align: 'left',
    field: 'knowledge_base_file_names',
  },
  {
    name: 'chatbot_unique_name',
    label: 'Chatbot Unique Name',
    align: 'left',
    field: 'chatbot_unique_name',
    sortable: true,
  },
];

const rows = ref<Row[]>([
  {
    chatbot_name: 'Chatbot 1',
    chatbot_unique_name: 'Chatbot Unique Name 1',
    model_name: 'Model 1',
    system_prompt: 'System Prompt 1',
    welcome_prompt: 'Welcome Prompt 1',
    temperature: 'Temperature 1',
    type_name: 'Type Name 1',
    knowledge_base_file_names: ['Knowledge Base File Names 1'],
  },
]);

const chatbotList = ref([
  {
    chatbot_id: '1',
    chatbot_name: 'Chatbot 1',
    chatbot_unique_name: 'Chatbot Unique Name 1',
    model_name: 'Model 1',
    system_prompt: 'System Prompt 1',
    welcome_prompt: 'Welcome Prompt 1',
    temperature: 'Temperature 1',
    type_name: 'Type Name 1',
    knowledge_base_file_names: ['Knowledge Base File Names 1'],
  },
]);

const getChatbotList = async () => {
  // Fetch chatbot list from the server
  const response = await api.get('/chatbot-list');
  // console.log(response.data);

  // Sort by created_at to maintain chronological order (oldest first)
  const sortedData = response.data.sort((a: { created_at: string }, b: { created_at: string }) => {
    return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
  });

  // Update the rows with the fetched data
  chatbotList.value = sortedData;
  rows.value = sortedData;
};

const handleAddChatbot = async () => {
  // Handle add chatbot
  await router.push('/teacher/chatbot/new');
};

const handleSettingChatbot = async (row: Row) => {
  // Handle setting chatbot
  // Get the chatbot_id from chatbotList by row
  const chatbotId = chatbotList.value.find(
    (chatbot) => chatbot.chatbot_unique_name === row.chatbot_unique_name,
  )?.chatbot_id;
  // Redirect to the setting chatbot page
  await router.push({
    path: '/teacher/chatbot/' + chatbotId,
    query: {
      source: 'manage',
    },
  });
};

const handleDuplicateChatbot = (row: Row) => {
  // Handle duplicate chatbot
  try {
    // Prevent multiple clicks during duplication
    if (duplicatingChatbot.value === row.chatbot_unique_name) {
      return;
    }

    // Get the chatbot from chatbotList by row
    const originalChatbot = chatbotList.value.find(
      (chatbot) => chatbot.chatbot_unique_name === row.chatbot_unique_name,
    );

    if (!originalChatbot) {
      $q.notify({
        type: 'negative',
        message: 'Chatbot not found',
      });
      return;
    }

    // Show confirmation dialog
    $q.dialog({
      title: 'Duplicate Chatbot',
      message: `Are you sure you want to duplicate "${row.chatbot_name}"?`,
      cancel: true,
      persistent: true,
    }).onOk(() => {
      void (async () => {
        try {
          // Set loading state for this specific chatbot
          duplicatingChatbot.value = row.chatbot_unique_name;

          $q.notify({
            type: 'info',
            message: 'Duplicating chatbot, please wait...',
            timeout: 2000,
          });

          // Generate a unique name for the duplicate
          const currentTime = new Date().toLocaleString().replace(/[/: ]/g, '_');
          const duplicateName = `${originalChatbot.chatbot_name} Copy ${currentTime}`;

          // Create duplicate chatbot using existing add endpoint
          const duplicateData = {
            chatbot_name: duplicateName,
            model_name: originalChatbot.model_name,
            system_prompt: originalChatbot.system_prompt,
            welcome_prompt: originalChatbot.welcome_prompt,
            temperature: parseFloat(originalChatbot.temperature),
            type_name: originalChatbot.type_name,
            description: {},
          };

          const response = await api.post('/chatbot', duplicateData);

          if (response.data) {
            $q.notify({
              type: 'positive',
              message: 'Chatbot duplicated successfully!',
            });

            // Refresh the chatbot list
            await getChatbotList();
          }
        } catch (error: unknown) {
          console.error('Error duplicating chatbot:', error);
          const errorMessage =
            (error as { response?: { data?: { detail?: string } } })?.response?.data?.detail ||
            'Failed to duplicate chatbot';
          $q.notify({
            type: 'negative',
            message: errorMessage,
          });
        } finally {
          // Clear loading state
          duplicatingChatbot.value = null;
        }
      })();
    });
  } catch (error: unknown) {
    console.error('Error in handleDuplicateChatbot:', error);
    $q.notify({
      type: 'negative',
      message: 'An error occurred while duplicating the chatbot',
    });
  }
};

// const handleChat = (row: Row) => {
//   // Handle chat
//   // Get the chatbot_id from chatbotList by row
//   const chatbotId = chatbotList.value.find(chatbot => chatbot.chatbot_unique_name === row.chatbot_unique_name)?.chatbot_id;
//   // Redirect to the chat page
//   router.push('/chat/' + chatbotId + '/session/latest');
// }

onMounted(async () => {
  isFetching.value = true;

  await getChatbotList();
  isFetching.value = false;
});
</script>

<style scoped lang="scss">
@import 'src/css/app.scss';
.hint p {
  margin: 0;
}
</style>
