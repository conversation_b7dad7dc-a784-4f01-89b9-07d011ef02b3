// app global css in SCSS form
@import 'quasar.variables.scss';//颜色
$slight-gray: rgb(249 250 251);
$bg-gray-50: rgb(229, 231, 235);
$bg-gray-100: rgb(241, 243, 245);
$bt-blue: rgb(14, 165, 233);

// 定义背景颜色
$white-colors: (
  white: #ffffff,
  shadow-light: rgba(0, 0, 0, 0.1),
  shadow-dark: rgba(255, 255, 255, 0.1),
  bg-dark: #1f2937 // 深色模式背景
);
$slight-gray-colors: (
  slight-gray: $slight-gray,
  dark-bg: #1e293b, // 深色模式背景
  shadow-light: rgba(0, 0, 0, 0.05),
  shadow-dark: rgba(255, 255, 255, 0.1)
);

// 定义背景渐变
$bytewise-blue-gradient: linear-gradient(to right bottom, rgb(239, 246, 255), rgb(236, 254, 255));



// 背景样式
.bytewise-background-blue {
  background: $bytewise-blue-gradient;
  // 添加浏览器前缀以确保兼容性
  background: -webkit-linear-gradient(to right bottom, rgb(239, 246, 255), rgb(236, 254, 255));
  background: linear-gradient(to right bottom, rgb(239, 246, 255), rgb(236, 254, 255));
}

.bytewise-background-grey-100 {
  background-color: $bg-gray-100;
}

.bytewise-background-grey-50 {
  background-color: $bg-gray-50;
}

.bytewise-blue-button {
  background: $bt-blue;
}

.bytewise-background-white {
  background-color: map-get($white-colors, white);
}




