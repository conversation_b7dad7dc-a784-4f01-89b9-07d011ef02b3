<template>
  <q-layout view="hHh LpR lfr">
    <!-- Top Bar -->
    <q-header elevated>
      <q-toolbar class="bg-white text-dark">
        <q-toolbar-title class="text-weight-bold">
          <q-btn flat dense round icon="menu" aria-label="Menu" @click="toggleLeftDrawer" />
          Bytewise
        </q-toolbar-title>
        <div v-if="user" class="text-weight-bold">{{ user.full_name }}'s Bytewise</div>
      </q-toolbar>
    </q-header>

    <!-- Sidebar -->
    <q-drawer v-model="isLeftDrawerOpen" show-if-above side="left" bordered>
      <q-list >
        <!-- Homepage Button -->
        <q-item clickable v-ripple to="/student/homepage">
          <q-item-section avatar>
            <q-icon name="home" />
          </q-item-section>
          <q-item-section> Homepage </q-item-section>
        </q-item>

        <!-- Courses Dropdown -->
        <q-expansion-item icon="book" label="Courses" expand-separator default-opened>
          <q-list class="bytewise-background-grey-100" >
            <q-item
              v-for="course in courses"
              :key="course.course_id"
              clickable
              v-ripple
              :active="course.course_id === selectedCourseId"
              @click="navigateToCourse(course.course_id)"
            >
              <q-item-section>
                {{ course.course_title }}
              </q-item-section>
            </q-item>
          </q-list>

        </q-expansion-item>

        <!-- My Account Button -->
        <q-item clickable v-ripple to="/student/account">
          <q-item-section avatar>
            <q-icon name="account_circle" />
          </q-item-section>
          <q-item-section> My Account </q-item-section>
        </q-item>

        <!-- Logout Button -->
        <q-item clickable v-ripple @click="confirmLogout">
          <q-item-section avatar>
            <q-icon name="logout" />
          </q-item-section>
          <q-item-section> Logout </q-item-section>
        </q-item>
      </q-list>
    </q-drawer>

    <!-- Main Content -->
    <q-page-container>
      <router-view />
    </q-page-container>

    <!-- Footer -->
    <q-footer>
      <q-toolbar class="justify-center bg-white text-dark ">
        <div class="text-center">
          © 2024 Bytewise. All rights reserved.<br />
          Version: 3.0.0 Beta
        </div>
      </q-toolbar>
    </q-footer>
  </q-layout>
</template>

<script setup lang="ts">
import { onMounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useQuasar } from 'quasar';
import { ref } from 'vue';
import { api } from 'boot/axios';
import { onNotify } from 'src/utils/notifications';

const router = useRouter();
const route = useRoute();
const $q = useQuasar();
const isLeftDrawerOpen = ref(true);

const selectedCourseId = ref<string>('');

const toggleLeftDrawer = () => {
  isLeftDrawerOpen.value = !isLeftDrawerOpen.value;
};

interface User {
  full_name: string;
  email: string;
  role_name: string;
}

const user = ref<User | null>(null);

const fetchUserData = async () => {
  try {
    const response = await api.get('/user-info');
    user.value = response.data;
    // If the user is not a student, redirect to the login page
    if (user.value && user.value.role_name !== 'Student') {
      $q.notify({
        type: 'negative',
        message: 'You are not authorized to access this page',
      });
      await router.push('/login');
    }
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Failed to fetch user data: ' + String(error),
    });
    await router.push('/login');
  }
};

interface CourseResponseItem {
  courses: Course;
}

interface Course {
  id: number;
  course_id: string;
  created_at: string;
  deleted_at: string;
  updated_at: string;
  description: JSON;
  course_title: string;
}
const courses = ref<Course[]>([]);

const fetchCourseList = async () => {
  try {
    const response = await api.get('/course-list');
    courses.value = response.data.map((item: CourseResponseItem) => item.courses);
    // console.log(courses.value);
    // Sort the courses by created_at from small to large
    courses.value.sort(
      (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
    );
  } catch (error) {
    $q.notify({
      type: 'negative',
      message: 'Failed to fetch courses: ' + String(error),
    });
  }
};

const navigateToCourse = async (courseId: string) => {
  selectedCourseId.value = courseId;
  await router.push(`/student/course/${courseId}`);
};

const confirmLogout = () => {
  $q.dialog({
    title: 'Alert',
    message: 'Are you sure to logout?',
    ok: 'Yes',
    cancel: 'No',
  })
    .onOk(() => {
      void (async () => {
        // User confirmed the logout
        // Remove the token from localStorage
        localStorage.removeItem('btws-tkn');
        // Redirect to the login page
        await router.push('/start');
        $q.notify({
          type: 'positive',
          message: 'Logout success',
        });
      })();
    })
    .onCancel(() => {
      // User cancelled the logout
    })
    .onDismiss(() => {
      // Triggered on both OK and Cancel
    });
};

const getStringParam = (param: string | string[]): string => {
  return Array.isArray(param) ? param[0] || '' : param;
};

watch(route, (newRoute) => {
  selectedCourseId.value = getStringParam(newRoute.params.courseId || '');
});

onMounted(async () => {
  onNotify($q);
  await fetchUserData();
  await fetchCourseList();
  selectedCourseId.value = getStringParam(route.params.courseId || '');
});
</script>

<style scoped>
.q-drawer {
  width: 200px;
}
</style>
