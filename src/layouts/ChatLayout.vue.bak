<template>
  <q-layout view="hHh LpR lfr">
    <!-- Top Bar -->
    <q-header elevated class="bg-white text-dark">
      <q-toolbar>
        <q-toolbar-title class="text-weight-bold">
          <q-btn flat dense round icon="menu" aria-label="Menu" @click="toggleLeftDrawer">
            <q-tooltip>Toggle Left Drawer</q-tooltip>
          </q-btn>
          <q-btn
            flat
            no-caps
            label="Bytewise"
            :to="urlToHomepage(user.role_name)"
            class="text-weight-bold"
            style="font-size: 1.3125rem; padding: 5px"
          />
        </q-toolbar-title>
        <q-space />
        <div class="q-gutter-sm">
          <q-btn flat dense round icon="link" @click="shareChatbot()">
            <q-tooltip>Share Chatbot</q-tooltip>
          </q-btn>
          <q-btn
            flat
            dense
            round
            icon="question_answer"
            @click="openConsultationMailbox"
            :color="hasUnreadInternalMessages ? 'red' : 'black'"
            v-if="user.role_name === 'Student'"
          >
            <q-tooltip>Consultation Mailbox</q-tooltip>
            <q-badge v-if="unreadInternalMessagesCount > 0" color="red" floating>
              {{ unreadInternalMessagesCount }}
            </q-badge>
          </q-btn>
          <q-btn
            flat
            dense
            round
            icon="add"
            @click="startNewSession(selectedChatbotId, moduleId, false)"
          >
            <q-tooltip>New Session</q-tooltip>
          </q-btn>
          <q-btn
            v-if="previewState.htmlContent"
            flat
            dense
            round
            icon="description"
            @click="toggleDocumentPreview"
            :color="previewState.isVisible ? 'primary' : 'grey'"
          >
            <q-tooltip>{{ previewState.isVisible ? 'Hide' : 'Show' }} Document Preview</q-tooltip>
          </q-btn>
          <q-btn
            flat
            dense
            round
            icon="checklist"
            class="float-right"
            @click="isRightDrawerOpen = !isRightDrawerOpen"
            :color="hasRightDrawerContent ? 'primary' : 'grey'"
          >
            <q-tooltip>Toggle Right Drawer</q-tooltip>
          </q-btn>
        </div>
      </q-toolbar>
    </q-header>
    <q-drawer v-if="!isFetching" v-model="isLeftDrawerOpen" show-if-above side="left" bordered>
      <q-list bordered separator>
        <div class="q-pa-md text-h6 row q-gutter-sm">
          <q-btn flat dense round icon="arrow_back" @click="goToCourse(user.role_name)">
            <q-tooltip>Back to Course</q-tooltip>
          </q-btn>
          <div>
            {{ courseTitle }}
          </div>
        </div>
        <q-expansion-item
          v-for="chatbot in chatbots"
          :key="chatbot.chatbot_id"
          :label="chatbot.chatbot_name"
          :model-value="expandedChatbots.includes(chatbot.chatbot_id)"
          @update:model-value="(val) => toggleChatbotExpand(chatbot.chatbot_id, val)"
          icon="chat"
          @before-show="checkAvailableSession(chatbot.chatbot_id)"
        >
          <div class="relative-position">
            <q-item
              clickable
              v-if="chatbot.sessions.length === 0"
              @click="
                loadingChatbots.includes(chatbot.chatbot_id)
                  ? null
                  : startNewSession(chatbot.chatbot_id, moduleId, false)
              "
              :disable="loadingChatbots.includes(chatbot.chatbot_id)"
            >
              <q-item-section avatar v-if="loadingChatbots.includes(chatbot.chatbot_id)">
                <q-spinner color="primary" size="1em" />
              </q-item-section>
              <q-item-section>
                {{
                  loadingChatbots.includes(chatbot.chatbot_id)
                    ? 'Loading sessions...'
                    : 'Start New Session'
                }}
              </q-item-section>
            </q-item>
            <q-item
              clickable
              v-for="session in chatbot.sessions"
              :key="session.session_id"
              :active="session.session_id === selectedSessionId"
              @click="selectSession(chatbot.chatbot_id, session.session_id, session.session_name)"
            >
              <q-item-section> {{ session.session_name }} </q-item-section>
              <template v-if="session.session_id === selectedSessionId">
                <q-btn
                  size="12px"
                  flat
                  dense
                  round
                  icon="edit"
                  @click.stop="editSession(session.session_id, session.session_name)"
                >
                  <q-tooltip>Edit Session Name</q-tooltip>
                </q-btn>
                <q-btn
                  size="12px"
                  flat
                  dense
                  round
                  icon="delete"
                  @click.stop="deleteSession(session.session_id)"
                >
                  <q-tooltip>Delete Session</q-tooltip>
                </q-btn>
              </template>
            </q-item>
          </div>
        </q-expansion-item>
      </q-list>
    </q-drawer>

    <!-- Add Right Drawer -->
    <q-drawer v-if="!isFetching" v-model="isRightDrawerOpen" side="right" bordered show-if-above>
      <q-scroll-area class="fit">
        <q-list>
          <!-- YouTube Video Player Section -->
          <div v-if="showYoutubePlayer && youtubeVideos.length > 0">
            <q-item-label header class="text-h6 q-pa-md text-dark">
              <div class="row items-center justify-between no-wrap">
                <div class="col row items-center no-wrap">
                  <q-icon name="video_library" class="q-mr-sm" color="primary" />
                  <span class="text-no-wrap">YouTube Videos</span>
                  <span
                    v-if="youtubeVideos.length > 1"
                    class="text-caption bg-primary text-white q-px-sm q-py-xs rounded-borders q-ml-sm"
                  >
                    {{ currentVideoIndex + 1 }} of {{ youtubeVideos.length }}
                  </span>
                </div>
                <div class="col-auto">
                  <q-btn
                    flat
                    dense
                    round
                    :icon="isYoutubePlayerMinimized ? 'expand_more' : 'expand_less'"
                    @click="minimizeYoutubePlayer"
                    color="primary"
                    size="sm"
                  >
                    <q-tooltip>{{
                      isYoutubePlayerMinimized ? 'Expand Videos' : 'Minimize Videos'
                    }}</q-tooltip>
                  </q-btn>
                </div>
              </div>
            </q-item-label>

            <div v-if="!isYoutubePlayerMinimized">
              <!-- Video iframe container -->
              <div class="youtube-container q-pa-md">
                <iframe
                  :src="currentYoutubeEmbedUrl"
                  frameborder="0"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowfullscreen
                  class="youtube-iframe"
                ></iframe>
              </div>

              <!-- Current Video Info (below video iframe) -->
              <q-card
                v-if="youtubeVideos.length > 0 && youtubeVideos[currentVideoIndex]"
                flat
                bordered
                class="q-mx-md bg-blue-1"
              >
                <q-card-section class="q-py-sm">
                  <div class="text-caption text-primary row items-center">
                    <q-icon name="play_circle_filled" size="sm" class="q-mr-sm" color="primary" />
                    <span class="text-weight-medium">
                      Video {{ currentVideoIndex + 1 }}:
                      {{ youtubeVideos[currentVideoIndex]?.title }}
                    </span>
                  </div>
                </q-card-section>
              </q-card>

              <!-- Video Navigation Controls (completely below video) -->
              <q-card
                v-if="youtubeVideos.length > 1"
                flat
                bordered
                class="q-mx-md q-mb-sm bg-grey-1"
              >
                <q-card-section class="q-py-sm">
                  <div class="row items-center no-wrap q-gutter-sm">
                    <div class="col-auto">
                      <q-btn
                        flat
                        dense
                        round
                        icon="chevron_left"
                        @click="previousVideo"
                        :disable="currentVideoIndex === 0"
                        color="primary"
                      >
                        <q-tooltip>Previous Video</q-tooltip>
                      </q-btn>
                      <q-btn
                        flat
                        dense
                        round
                        icon="chevron_right"
                        @click="nextVideo"
                        :disable="currentVideoIndex === youtubeVideos.length - 1"
                        color="primary"
                      >
                        <q-tooltip>Next Video</q-tooltip>
                      </q-btn>
                    </div>

                    <!-- Video Selection Dropdown -->
                    <div class="col">
                      <q-select
                        v-model="currentVideoIndex"
                        dense
                        outlined
                        :options="videoSelectOptions"
                        option-value="value"
                        option-label="label"
                        emit-value
                        map-options
                        bg-color="white"
                        color="primary"
                      >
                        <template v-slot:prepend>
                          <q-icon name="video_library" color="primary" />
                        </template>
                        <q-tooltip
                          v-if="youtubeVideos[currentVideoIndex]?.title"
                          class="bg-primary"
                        >
                          Full title: {{ youtubeVideos[currentVideoIndex]?.title }}
                        </q-tooltip>
                      </q-select>
                    </div>

                    <div class="col-auto">
                      <!-- Navigation hint for mobile users -->
                      <q-chip dense color="primary" text-color="white" class="text-caption">
                        {{ currentVideoIndex + 1 }} / {{ youtubeVideos.length }}
                      </q-chip>
                    </div>
                  </div>
                </q-card-section>
              </q-card>
            </div>
          </div>

          <!-- Add Session Summary Section -->
          <div v-show="chatSessionDescription.qualitative_report.session_summary">
            <q-item-label header class="text-h6 q-pa-md text-dark"> Session Summary </q-item-label>

            <q-item-section>
              <div
                v-if="chatSessionDescription.qualitative_report.session_summary"
                class="sessionsummary-container q-px-md"
              >
                <div
                  v-html="
                    markdown.render(chatSessionDescription.qualitative_report.session_summary)
                  "
                ></div>
              </div>
              <div v-else class="text-center text-grey">No session summary available.</div>
            </q-item-section>
          </div>



          <!-- Add Progress Checklist Section (only for ChecklistChatBot) -->
          <div
            v-if="
              currentChatbotDetail.type_name === 'ChecklistChatBot' &&
              chatSessionDescription.checklist_progress
            "
          >
            <q-item-label header class="text-h6 q-pa-md text-dark">
              Progress Checklist
            </q-item-label>

            <template v-if="Array.isArray(chatSessionDescription.checklist_progress)">
              <!-- Progress Summary -->
              <q-item>
                <q-item-section>
                  <q-linear-progress :value="checklistProgress" color="positive" />
                  <div class="text-caption text-center q-mt-sm">
                    {{ Math.round(checklistProgress * 100) }}% Complete
                  </div>
                </q-item-section>
              </q-item>

              <q-item v-for="item in chatSessionDescription.checklist_progress" :key="item.item_id">
                <q-item-section avatar>
                  <q-checkbox
                    v-model="item.completed"
                    disable
                    :color="item.completed ? 'positive' : 'grey'"
                  />
                </q-item-section>
                <q-item-section>
                  <q-item-label :class="{ 'text-positive': item.completed }">
                    {{ item.item_content }}
                  </q-item-label>
                </q-item-section>
              </q-item>
            </template>

            <q-item v-else>
              <q-item-section class="text-center text-grey">
                No checklist items available
              </q-item-section>
            </q-item>
          </div>
        </q-list>
      </q-scroll-area>
    </q-drawer>

    <!-- Consultation Mailbox Dialog -->
    <q-dialog v-model="showConsultationMailbox" persistent>
      <q-card style="width: 700px; max-width: 80vw">
        <q-card-section class="row items-center q-pb-none">
          <div class="text-h6">
            {{ user.role_name === 'Student' ? 'Raise Hand / Ask Teacher' : 'Internal Messages' }}
          </div>
          <q-space />
          <q-btn icon="close" flat round dense v-close-popup />
        </q-card-section>

        <q-card-section>
          <div class="text-subtitle2 q-mb-md">Session: {{ sessionName || 'Current Session' }}</div>

          <!-- Messages Area -->
          <div
            class="consultation-messages-container q-pa-md"
            style="height: 300px; overflow-y: auto; border: 1px solid #ddd; border-radius: 4px"
            ref="messagesContainer"
          >
            <div v-if="internalMessages.length === 0" class="text-center text-grey-6 q-mt-md">
              <q-icon name="message" size="md" class="q-mb-sm" />
              <div>
                {{
                  user.role_name === 'Student'
                    ? 'Start a conversation with your teacher'
                    : 'No internal messages yet'
                }}
              </div>
            </div>

            <div v-for="message in internalMessages" :key="message.message_id" class="q-mb-md">
              <q-chat-message
                :name="message.sender_role === 'Student' ? 'Student' : 'Teacher'"
                :sent="message.sender_role === user.role_name"
                :text-color="message.sender_role === user.role_name ? 'white' : 'black'"
                :bg-color="message.sender_role === user.role_name ? 'blue-7' : 'grey-4'"
                :stamp="formatMessageTime(message.created_at || '')"
              >
                <div v-html="markdown.render(message.message_content)"></div>
              </q-chat-message>
            </div>
          </div>

          <!-- Message Input -->
          <div class="q-mt-md">
            <q-input
              v-model="newInternalMessage"
              type="textarea"
              :label="user.role_name === 'Student' ? 'Ask your teacher...' : 'Reply to student...'"
              :rows="3"
              outlined
              @keyup.ctrl.enter="sendInternalMessage"
            >
              <template v-slot:append>
                <q-btn
                  round
                  dense
                  flat
                  icon="send"
                  @click="sendInternalMessage"
                  :disable="!newInternalMessage.trim()"
                  :loading="sendingInternalMessage"
                />
              </template>
            </q-input>
            <div class="text-caption text-grey-6 q-mt-xs">Press Ctrl+Enter to send</div>
          </div>
        </q-card-section>
      </q-card>
    </q-dialog>

    <q-page-container v-if="!isFetching">
      <div class="chat-layout-wrapper">
        <!-- Main Chat Content -->
        <div class="chat-main-content" :class="{ 'with-document-preview': previewState.isVisible }">
          <router-view />
        </div>
        
        <!-- Document Preview Sidebar -->
        <div 
          v-if="previewState.isVisible && previewState.htmlContent" 
          class="document-preview-sidebar"
          :style="{ width: sidebarWidth + 'px' }"
        >
          <!-- Resize handle for desktop -->
          <div 
            class="sidebar-resize-handle"
            @mousedown="startResize"
            @touchstart="startResize"
          >
            <div class="resize-handle-line"></div>
          </div>

          <div class="document-sidebar-header">
            <div class="row items-center justify-between no-wrap q-pa-md">
              <div class="col row items-center no-wrap">
                <q-icon name="description" class="q-mr-sm" color="primary" />
                <span class="text-h6">Document Preview</span>
                <span
                  v-if="previewState.documents.length > 1"
                  class="text-caption bg-primary text-white q-px-sm q-py-xs rounded-borders q-ml-sm"
                >
                  {{ currentDocumentIndex + 1 }} of {{ previewState.documents.length }}
                </span>
              </div>
              <div class="col-auto">
                <q-btn
                  flat
                  dense
                  round
                  icon="fullscreen"
                  @click="openFullscreenPreview"
                  color="primary"
                  size="sm"
                  class="q-mr-sm"
                >
                  <q-tooltip>Open in fullscreen</q-tooltip>
                </q-btn>
                <q-btn
                  flat
                  dense
                  round
                  icon="close"
                  @click="hideDocumentPreview"
                  color="grey"
                  size="sm"
                >
                  <q-tooltip>Close preview</q-tooltip>
                </q-btn>
              </div>
            </div>
          </div>

          <!-- Document Navigation (for multiple documents) -->
          <div v-if="previewState.documents.length > 1" class="document-navigation q-px-md q-pb-md">
            <q-card flat bordered class="bg-grey-1">
              <q-card-section class="q-py-sm">
                <div class="row items-center no-wrap q-gutter-sm">
                  <div class="col-auto">
                    <q-btn
                      flat
                      dense
                      round
                      icon="chevron_left"
                      @click="previousDocument"
                      :disable="currentDocumentIndex === 0"
                      color="primary"
                      size="sm"
                    >
                      <q-tooltip>Previous Document</q-tooltip>
                    </q-btn>
                    <q-btn
                      flat
                      dense
                      round
                      icon="chevron_right"
                      @click="nextDocument"
                      :disable="currentDocumentIndex === previewState.documents.length - 1"
                      color="primary"
                      size="sm"
                    >
                      <q-tooltip>Next Document</q-tooltip>
                    </q-btn>
                  </div>

                  <!-- Document Selection Dropdown -->
                  <div class="col">
                    <q-select
                      v-model="previewState.activeDocumentId"
                      dense
                      outlined
                      :options="documentSelectOptions"
                      option-value="value"
                      option-label="label"
                      emit-value
                      map-options
                      bg-color="white"
                      color="primary"
                      @update:model-value="switchToDocumentFromSelect"
                    >
                      <template v-slot:prepend>
                        <q-icon name="description" color="primary" size="sm" />
                      </template>
                      <q-tooltip
                        v-if="currentDocument?.title"
                        class="bg-primary"
                      >
                        Full title: {{ currentDocument.title }}
                      </q-tooltip>
                    </q-select>
                  </div>

                  <div class="col-auto">
                    <q-chip dense color="primary" text-color="white" class="text-caption">
                      {{ currentDocumentIndex + 1 }} / {{ previewState.documents.length }}
                    </q-chip>
                  </div>
                </div>
              </q-card-section>
            </q-card>
          </div>

          <!-- Document Preview Content -->
          <div class="document-content">
            <DocumentPreview
              :html-content="previewState.htmlContent"
              mode="sidebar"
              :visible="previewState.isVisible"
              :title="previewState.title"
              @close="hideDocumentPreview"
              @fullscreen="openFullscreenPreview"
              class="document-preview-component"
            />
          </div>
        </div>
      </div>
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted, provide, computed, nextTick } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useQuasar } from 'quasar';
import { api } from 'boot/axios';
import MarkdownIt from 'markdown-it';
import MarkdownItKatex from 'markdown-it-katex';
import DocumentPreview from '../components/DocumentPreview.vue';
import { previewState, hideDocumentPreview, switchToDocument } from '../utils/documentPreview';
const markdown = new MarkdownIt();
markdown.use(MarkdownItKatex);

// Define interfaces
interface Session {
  session_id: string;
  session_name: string;
  session_index: number;
}

interface Chatbot {
  chatbot_name: string;
  chatbot_id: string;
  created_at: string;
  sessions: Session[];
  isSessionsLoading?: boolean;
}

interface User {
  full_name: string;
  email: string;
  role_name: string;
}

interface ChatLayoutCache {
  timestamp: number;
  chatbots: Chatbot[];
  user: User;
  moduleId: string;
}

// Add new interfaces for the API conversation types
interface ApiMessage {
  role: string;
  content: string;
}

interface ChatbotDetail {
  chatbot_id: string;
  id: number;
  created_at: string;
  chatbot_name: string;
  model_name: string;
  system_prompt: string;
  welcome_prompt: string;
  temperature: number;
  type_name: string;
  description: {
    checklist_items: string;
    session_summary_prompt: string;
  };
  knowledge_base_persist_directory: string;
  knowledge_base_file_paths: string[];
  knowledge_base_file_names: string[];
  knowledge_base_embedding_model: string;
  updated_at: string;
  deleted_at: string;
}

interface ChecklistProgressItem {
  item_id: string;
  item_content: string;
  completed: boolean;
}

interface QuantitativeReport {
  turn_count: number;
  user_word_count: number;
  chatbot_word_count: number;
  conversation_time: number;
}

interface QualitativeReport {
  session_summary: string;
}

interface UserFeedbackItem {
  rating: number;
  text_feedback: string;
}

interface ChatSessionDescription {
  checklist_progress: ChecklistProgressItem[];
  quantitative_report: QuantitativeReport;
  qualitative_report: QualitativeReport;
  user_feedback: UserFeedbackItem;
}

// Initialize chatSessionDescription with a default quantitative_report
const chatSessionDescription = ref<ChatSessionDescription>({
  checklist_progress: [],
  quantitative_report: {
    turn_count: 0,
    user_word_count: 0,
    chatbot_word_count: 0,
    conversation_time: 0,
  },
  qualitative_report: {
    session_summary: '',
  },
  user_feedback: {
    rating: 0,
    text_feedback: '',
  },
});

// Create cache storage
const layoutCache = new Map<string, ChatLayoutCache>();

// Cache expiration time (ms) - set to 5 minutes
const CACHE_EXPIRY = 5 * 60 * 1000;

// Function to clean up expired cache
const cleanExpiredCache = () => {
  const now = Date.now();
  for (const [key, value] of layoutCache.entries()) {
    if (now - value.timestamp > CACHE_EXPIRY) {
      layoutCache.delete(key);
    }
  }
};

// Clean up expired cache periodically
const cleanupInterval = setInterval(cleanExpiredCache, 60 * 1000);

// Clean up the timer when the component is unmounted
onUnmounted(() => {
  clearInterval(cleanupInterval);
});

const getStringParam = (param: string | string[]): string => {
  return Array.isArray(param) ? param[0] || '' : param;
};

const $q = useQuasar();
const router = useRouter();
const route = useRoute();

// Drawer state
const isLeftDrawerOpen = ref(true);
const isRightDrawerOpen = ref(false);

const toggleLeftDrawer = () => {
  isLeftDrawerOpen.value = !isLeftDrawerOpen.value;
};

const chatbots = ref([
  {
    chatbot_name: 'Chatbot 1',
    chatbot_id: '1',
    created_at: '2024-06-29T11:05:22.8929+00:00',
    sessions: [
      { session_id: '1', session_name: 'Session 1 (Latest)', session_index: 1 },
      { session_id: '2', session_name: 'Session 2', session_index: 2 },
    ],
  },
  {
    chatbot_name: 'Chatbot 2',
    chatbot_id: '2',
    created_at: '2024-06-29T11:06:22.8929+00:00',
    sessions: [
      { session_id: '1', session_name: 'Session 1', session_index: 1 },
      { session_id: '2', session_name: 'Session 2', session_index: 2 },
      { session_id: '3', session_name: 'Session 3', session_index: 3 },
    ],
  },
  {
    chatbot_name: 'Chatbot 3',
    chatbot_id: '3',
    created_at: '2024-06-29T11:07:22.8929+00:00',
    sessions: [{ session_id: '1', session_name: 'Session 1', session_index: 1 }],
  },
  {
    chatbot_name: 'Chatbot 4',
    chatbot_id: '4',
    created_at: '2024-06-29T11:08:22.8929+00:00',
    sessions: [
      { session_id: '1', session_name: 'Session 1', session_index: 1 },
      { session_id: '2', session_name: 'Session 2', session_index: 2 },
    ],
  },
]);

const isFetching = ref(true);
const sessionId = ref<string>('');
const selectedSessionId = ref<string>('');
const selectedChatbotId = ref<string>('');
const courseId = ref<string>('');
const courseTitle = ref<string>('');
const moduleId = ref<string>('');
const moduleTitle = ref<string>('');
// Create a ref to store the expanded chatbots
const expandedChatbots = ref<string[]>([]);

interface User {
  full_name: string;
  email: string;
  role_name: string;
}

const user = ref<User>({
  full_name: '',
  email: '',
  role_name: '',
});

const fetchUserInfo = async () => {
  // Generate cache key for user info
  const cacheKey = 'user-info';

  // Check cache first
  const cachedData = layoutCache.get(cacheKey);
  const now = Date.now();

  if (cachedData && now - cachedData.timestamp < CACHE_EXPIRY) {
    user.value = cachedData.user;
    return;
  }

  // If no cache or expired, fetch from server
  const response = await api.get('/user-info');
  user.value = response.data;

  // Update cache
  layoutCache.set(cacheKey, {
    timestamp: now,
    chatbots: chatbots.value,
    user: response.data,
    moduleId: moduleId.value,
  });
};

const urlToHomepage = (role: string) => {
  if (role === 'Teacher') {
    return '/teacher/homepage';
  } else if (role === 'Student') {
    return '/student/homepage';
  } else {
    return '/';
  }
};

const goToCourse = async (role: string) => {
  await router.push({
    path: `${urlToHomepage(role).replace('/homepage', '')}/course/${courseId.value}`,
  });
};

const checkAvailableSession = async (chatbot: string) => {
  // Only fetch sessions if the chatbot is not expanded
  const chatbotItem = chatbots.value.find((c) => c.chatbot_id === chatbot);
  if (chatbotItem && chatbotItem.sessions.length === 0) {
    await fetchChatbotSessions(chatbot);

    // Check if the session is still empty after fetching
    const updatedChatbot = chatbots.value.find((c) => c.chatbot_id === chatbot);
    if (updatedChatbot && updatedChatbot.sessions.length === 0) {
      $q.notify({
        type: 'info',
        message: 'No previous session available for this chatbot, please start a new session.',
      });
    }
  }
};

// Fetch chatbot list by module
const fetchChatbotListByModule = async () => {
  // Generate cache key based on moduleId for the chatbot list only
  const cacheKey = `chatbots-list-${moduleId.value}`;

  // Check cache first
  const cachedData = layoutCache.get(cacheKey);
  const now = Date.now();

  if (cachedData && now - cachedData.timestamp < CACHE_EXPIRY) {
    // Create a map of existing chatbots to preserve sessions
    const existingChatbotsMap = new Map(
      chatbots.value.map((chatbot) => [chatbot.chatbot_id, chatbot.sessions]),
    );

    // Use cached chatbot list but preserve any existing sessions
    chatbots.value = cachedData.chatbots.map((chatbot) => ({
      ...chatbot,
      sessions: existingChatbotsMap.get(chatbot.chatbot_id) || [], // Preserve existing sessions if available
    }));
    return;
  }

  // If no cache or expired, fetch from server
  try {
    // The chatbot list is stored in the modules_chatbots table
    const response = await api.get('/modules-chatbots', {
      params: {
        module_id: moduleId.value,
      },
    });

    if (!response.data || response.data.length === 0) {
      chatbots.value = [];
      return;
    }

    // Create a map of existing chatbots to preserve sessions
    const existingChatbotsMap = new Map(
      chatbots.value.map((chatbot) => [chatbot.chatbot_id, chatbot.sessions]),
    );

    // Convert the fetched chatbot list to the format we need
    chatbots.value = response.data.map(
      (chatbot: {
        chatbot_id: string;
        chatbot_name: string;
        created_at: string;
        [key: string]: unknown;
      }) => ({
        ...chatbot,
        sessions: existingChatbotsMap.get(chatbot.chatbot_id) || [], // Preserve existing sessions if available
      }),
    );

    // Sort by created_at
    chatbots.value.sort(
      (a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime(),
    );

    // Update cache
    layoutCache.set(cacheKey, {
      timestamp: now,
      chatbots: chatbots.value.map((chatbot) => ({ ...chatbot, sessions: [] })), // Don't cache sessions in this cache
      user: user.value,
      moduleId: moduleId.value,
    });
  } catch (error) {
    console.error('Error fetching chatbot list:', error);
    $q.notify({
      type: 'negative',
      message: 'Failed to load chatbots.',
    });
  }
};

// Add a new ref to track which chatbots are currently loading sessions
const loadingChatbots = ref<string[]>([]);

// Modify fetchChatbotSessions function to show loading state when requesting
const fetchChatbotSessions = async (chatbotId: string) => {
  try {
    // Generate cache key for this specific chatbot
    const cacheKey = `chatbot-sessions-${moduleId.value}-${chatbotId}`;

    // Check cache first
    const cachedData = layoutCache.get(cacheKey);
    const now = Date.now();

    if (cachedData && now - cachedData.timestamp < CACHE_EXPIRY) {
      // Find and update this chatbot's sessions from cache
      const chatbotIndex = chatbots.value.findIndex((c) => c.chatbot_id === chatbotId);
      if (chatbotIndex !== -1) {
        const chatbot = JSON.parse(JSON.stringify(chatbots.value[chatbotIndex]));
        // Find the corresponding chatbot in cache
        const cachedChatbot = cachedData.chatbots.find((c) => c.chatbot_id === chatbotId);
        if (cachedChatbot) {
          chatbot.sessions = cachedChatbot.sessions;
          chatbots.value[chatbotIndex] = chatbot as Chatbot;
        }
      }
      return;
    }

    // If no cache or expired, fetch from server
    // Show loading indicator
    loadingChatbots.value.push(chatbotId);

    const response = await api.get('/chatbot-session-list-by-module-and-chatbot', {
      params: {
        module_id: moduleId.value,
        chatbot_id: chatbotId,
      },
    });

    // If response is empty, set empty sessions array
    if (!response.data || Object.keys(response.data).length === 0) {
      const chatbotIndex = chatbots.value.findIndex((c) => c.chatbot_id === chatbotId);
      if (chatbotIndex !== -1) {
        const updatedChatbot = JSON.parse(JSON.stringify(chatbots.value[chatbotIndex]));
        updatedChatbot.sessions = [];
        chatbots.value[chatbotIndex] = updatedChatbot as Chatbot;
      }

      // Remove loading indicator
      loadingChatbots.value = loadingChatbots.value.filter((id) => id !== chatbotId);
      return;
    }

    // Update the specific chatbot with the fetched data
    const chatbotIndex = chatbots.value.findIndex((c) => c.chatbot_id === chatbotId);
    if (chatbotIndex !== -1) {
      const updatedChatbot = JSON.parse(JSON.stringify(chatbots.value[chatbotIndex]));
      updatedChatbot.sessions = response.data.sessions || [];

      // Sort sessions by session_index from large to small
      updatedChatbot.sessions.sort((a: Session, b: Session) => b.session_index - a.session_index);
      chatbots.value[chatbotIndex] = updatedChatbot as Chatbot;

      // Update cache for this specific chatbot
      layoutCache.set(cacheKey, {
        timestamp: now,
        chatbots: [updatedChatbot as Chatbot], // Store only this chatbot
        user: user.value,
        moduleId: moduleId.value,
      });
    }

    // Remove loading indicator
    loadingChatbots.value = loadingChatbots.value.filter((id) => id !== chatbotId);
  } catch (error) {
    console.error('Error fetching chatbot sessions:', error);

    // Remove loading indicator
    loadingChatbots.value = loadingChatbots.value.filter((id) => id !== chatbotId);

    $q.notify({
      type: 'negative',
      message: 'Failed to load chatbot sessions.',
    });
  }
};

const getChatbotShareLink = () => {
  const origin = window.location.origin;
  const chatbotId = encodeURIComponent(selectedChatbotId.value || '');

  const params = new URLSearchParams({
    courseId: courseId.value || '',
    courseTitle: courseTitle.value || '',
    moduleId: moduleId.value || '',
    moduleTitle: moduleTitle.value || '',
  });

  return `${origin}/#/chat/${chatbotId}/session/latest?${params.toString()}`;
};

const shareChatbot = async () => {
  // Get current chatbot name
  const chatbotName =
    chatbots.value.find((chatbot) => chatbot.chatbot_id === selectedChatbotId.value)
      ?.chatbot_name || 'Unknown Chatbot';

  // Copy the share link to the client's clipboard
  await navigator.clipboard.writeText(getChatbotShareLink());
  $q.notify({
    type: 'positive',
    message: 'The share link has been copied to your clipboard.',
  });
  $q.dialog({
    title: 'Chatbot Shared',
    message:
      'The current chatbot (' +
      chatbotName +
      ') has been shared. Please ensure that the student user who will be accessing this link has joined the corresponding student group for this course (' +
      courseTitle.value +
      '). The share link has been copied to your clipboard: ',
    prompt: {
      model: getChatbotShareLink(),
      type: 'text',
      dense: true,
      readonly: true,
    },
    ok: 'OK',
  });
};

// Modify the startNewSession function to update cache properly
const startNewSession = async (chatbot: string, module: string, silent: boolean) => {
  if (!silent) {
    $q.dialog({
      title: 'Start New Session',
      message: 'Are you sure you want to start a new session?',
      ok: 'Yes',
      cancel: 'No',
    }).onOk(() => {
      void (async () => {
        const response = await api.post('/chat-session', {
          chatbot_id: chatbot,
          module_id: module,
        });

        // Invalidate both caches for this module and chatbot
        layoutCache.delete(`chatbots-${module}`);
        layoutCache.delete(`chatbot-sessions-${module}-${chatbot}`);

        // Create a new session object
        const newSession: Session = {
          session_id: response.data.session_id,
          session_name: response.data.session_name,
          session_index: response.data.session_index || 999, // Use a high number if not provided
        };

        // Update local session list
        updateLocalSessionList(chatbot, newSession, 'add');

        await selectSession(chatbot, response.data.session_id, response.data.session_name);
      })();
    });
  } else {
    const response = await api.post('/chat-session', {
      chatbot_id: chatbot,
      module_id: module,
    });

    // Invalidate both caches for this module and chatbot
    layoutCache.delete(`chatbots-${module}`);
    layoutCache.delete(`chatbot-sessions-${module}-${chatbot}`);

    // Create a new session object
    const newSession: Session = {
      session_id: response.data.session_id,
      session_name: response.data.session_name,
      session_index: response.data.session_index || 999, // Use a high number if not provided
    };

    // Update local session list
    updateLocalSessionList(chatbot, newSession, 'add');

    await selectSession(chatbot, response.data.session_id, response.data.session_name);
  }
};

// Update the updateLocalSessionList function to handle the new cache structure
const updateLocalSessionList = (
  chatbotId: string,
  updatedSession: Session | null,
  action: 'add' | 'update' | 'delete',
) => {
  // Find the chatbot in the list
  const chatbotIndex = chatbots.value.findIndex((c) => c.chatbot_id === chatbotId);
  if (chatbotIndex === -1) return;

  // Create a deep copy of the chatbot to avoid modifying the original directly
  const chatbotCopy: Chatbot = JSON.parse(JSON.stringify(chatbots.value[chatbotIndex]));

  if (action === 'add' && updatedSession) {
    // Add the new session to the beginning of the list (it will be the latest)
    chatbotCopy.sessions.unshift(updatedSession);
    // Re-sort sessions by session_index
    chatbotCopy.sessions.sort((a, b) => b.session_index - a.session_index);
  } else if (action === 'update' && updatedSession) {
    // Find and update the session
    const sessionIndex = chatbotCopy.sessions.findIndex(
      (s) => s.session_id === updatedSession.session_id,
    );
    if (sessionIndex !== -1) {
      chatbotCopy.sessions[sessionIndex] = updatedSession;
    }
  } else if (action === 'delete' && updatedSession) {
    // Remove the session
    chatbotCopy.sessions = chatbotCopy.sessions.filter(
      (s) => s.session_id !== updatedSession.session_id,
    );
  }

  // Update the chatbots array with the modified copy
  chatbots.value[chatbotIndex] = chatbotCopy;

  // Update the cache with the modified data
  const cacheKey = `chatbot-sessions-${moduleId.value}-${chatbotId}`;
  const now = Date.now();
  layoutCache.set(cacheKey, {
    timestamp: now,
    chatbots: [chatbotCopy], // Store only this chatbot
    user: user.value,
    moduleId: moduleId.value,
  });
};

// Update deleteSession to handle the new cache structure
const deleteSession = (sessionId: string) => {
  $q.dialog({
    title: 'Delete Session',
    message: 'Are you sure you want to delete this session?',
    ok: 'Yes',
    cancel: 'No',
  }).onOk(() => {
    void (async () => {
      // Find the chatbot that contains this session
      let chatbotId = '';
      let sessionToDelete: Session | null = null;

      for (const chatbot of chatbots.value) {
        const session = chatbot.sessions.find((s) => s.session_id === sessionId);
        if (session) {
          chatbotId = chatbot.chatbot_id;
          sessionToDelete = session;
          break;
        }
      }

      await api.delete('/chat-session', {
        params: {
          session_id: sessionId,
        },
      });

      // Invalidate cache for this specific chatbot
      layoutCache.delete(`chatbot-sessions-${moduleId.value}-${chatbotId}`);

      // Update local session list if we found the session
      if (chatbotId && sessionToDelete) {
        updateLocalSessionList(chatbotId, sessionToDelete, 'delete');
      } else {
        // Fallback to fetching if we couldn't find the session locally
        if (chatbotId) {
          await fetchChatbotSessions(chatbotId);
        }
      }

      $q.notify({
        type: 'positive',
        message: 'Session deleted successfully.',
      });
    })();
  });
};

// Update editSession to handle the new cache structure
const editSession = (sessionId: string, currentName: string) => {
  $q.dialog({
    title: 'Edit Session Name',
    message: 'Enter new session name:',
    prompt: {
      model: currentName,
      type: 'text',
      isValid: (val) => val.length > 0 && val !== currentName, // Check if input is not empty and different from current name
    },
    cancel: true,
    persistent: true,
  }).onOk((newName) => {
    void (async () => {
      try {
        // Find the chatbot that contains this session
        let chatbotId = '';
        let sessionToUpdate: Session | null = null;

        for (const chatbot of chatbots.value) {
          const session = chatbot.sessions.find((s) => s.session_id === sessionId);
          if (session) {
            chatbotId = chatbot.chatbot_id;
            sessionToUpdate = { ...session, session_name: newName };
            break;
          }
        }

        // Call the API to update the session name
        await api.put('/chat-session-name', {
          session_id: sessionId,
          session_name: newName,
        });

        // Invalidate cache for this specific chatbot
        layoutCache.delete(`chatbot-sessions-${moduleId.value}-${chatbotId}`);

        // Update local session list if we found the session
        if (chatbotId && sessionToUpdate) {
          updateLocalSessionList(chatbotId, sessionToUpdate, 'update');
        } else {
          // Fallback to fetching if we couldn't find the session locally
          if (chatbotId) {
            await fetchChatbotSessions(chatbotId);
          }
        }

        // If the session name is updated, update the route query parameters
        if (sessionId === selectedSessionId.value) {
          await router.replace({
            query: {
              ...route.query,
              sessionName: newName,
            },
          });
        }

        $q.notify({
          type: 'positive',
          message: 'Session name updated successfully.',
        });
      } catch {
        $q.notify({
          type: 'negative',
          message: 'Failed to update session name.',
        });
      }
    })();
  });
};

// Add new refs for API conversation and selected chatbot detail
const currentApiConversation = ref<ApiMessage[]>([]);
const currentChatbotDetail = ref<ChatbotDetail>({
  chatbot_id: '',
  id: 0,
  created_at: '',
  chatbot_name: '',
  model_name: '',
  system_prompt: '',
  welcome_prompt: '',
  temperature: 0,
  type_name: '',
  description: {
    checklist_items: '',
    session_summary_prompt: '',
  },
  knowledge_base_persist_directory: '',
  knowledge_base_file_paths: [],
  knowledge_base_file_names: [],
  knowledge_base_embedding_model: '',
  updated_at: '',
  deleted_at: '',
});

// Provide the refs to child components
provide('apiConversation', currentApiConversation);
provide('chatbotDetail', currentChatbotDetail);

// Add new function to update checklist_progress and quantitative_report to API
const updateChatSessionDescQuantReport = async () => {
  try {
    // Calculate the new turn count based on user and chatbot messages
    const messages = currentApiConversation.value || []; // Default to an empty array if undefined
    let turnCount = 0;

    let skipNext = false; // Flag to skip the next assistant message
    for (const message of messages) {
      if (skipNext) {
        if (message.role === 'assistant') {
          continue; // Skip assistant messages
        }
        skipNext = false; // Reset the flag
      }

      if (message.role === 'user') {
        turnCount++; // Count user message
        skipNext = true; // Set the flag to skip the next assistant message
      }
    }

    chatSessionDescription.value.quantitative_report.turn_count = turnCount; // Update turn count

    // Calculate user_word_count and chatbot_word_count based on the current conversation
    const userMessages = messages.filter((msg) => msg.role === 'user');
    const chatbotMessages = messages.filter((msg) => msg.role === 'assistant');

    chatSessionDescription.value.quantitative_report.user_word_count = userMessages.reduce(
      (count, msg) => count + msg.content.split(' ').length,
      0,
    );
    chatSessionDescription.value.quantitative_report.chatbot_word_count = chatbotMessages.reduce(
      (count, msg) => count + msg.content.split(' ').length,
      0,
    );

    // Note: Only submit the nessary fields to the backend, the backend will handle the merge of description
    await api.put('/chat-session-description', {
      session_id: sessionId.value,
      description: {
        quantitative_report: chatSessionDescription.value.quantitative_report,
      },
    });

    // console.log('Update response:', response.data);
  } catch (error) {
    console.error('Error updating reports:', error);
    $q.notify({
      type: 'negative',
      message: 'Failed to update reports.',
    });
  }
};

// Remove the computed property for checklistProgress
const checklistProgress = computed(() => {
  const progress = chatSessionDescription.value?.checklist_progress;

  // Check if progress is available
  if (!progress || !Array.isArray(progress) || progress.length === 0) {
    return 0;
  }

  const completedItems = progress.filter((item) => item?.completed).length;
  return completedItems / progress.length;
});

// Function to fetch session summary from the backend
const fetchSessionSummary = async () => {
  try {
    const response = await api.post('/chat-session-summary', {
      session_id: sessionId.value,
      conversation_list: currentApiConversation.value,
      session_summary_prompt: currentChatbotDetail.value.description.session_summary_prompt,
    });
    // console.log('Session Summary Response:', response.data);
    chatSessionDescription.value.qualitative_report.session_summary = response.data;

    // console.log('Updating chat session description with:', chatSessionDescription.value);
  } catch (error) {
    console.error('Error fetching session summary:', error);
    $q.notify({
      type: 'negative',
      message: 'Failed to fetch session summary.',
    });
  }
};

const generateScript = async () => {
  try {
    await api.post('/generate-script', {
      session_id: sessionId.value,
      conversation_list: currentApiConversation.value,
    });
    await router.push('/teacher/generate-script?sessionId=' + sessionId.value);
  } catch (error) {
    console.error('Error fetching session summary:', error);
    $q.notify({
      type: 'negative',
      message: 'Failed to fetch session summary.',
    });
  }
};

// YouTube multiple videos detection logic
const showYoutubePlayer = ref(false);
const isYoutubePlayerMinimized = ref(false);
const youtubeVideos = ref<Array<{ id: string; url: string; title: string; messageIndex: number }>>(
  [],
);
const currentVideoIndex = ref(0);
const initialConversationProcessed = ref(false);

// Extract YouTube video ID from URL
const extractYoutubeId = (url: string): string | null => {
  const regexPatterns = [
    /(?:youtube\.com\/(?:[^/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?/\s]{11})/i,
    /(?:youtube\.com\/(?:watch\?v=|embed\/|v\/)|youtu\.be\/)([^"&?/\s]{11})/i,
  ];

  for (const pattern of regexPatterns) {
    const match = url.match(pattern);
    if (match && match[1]) {
      return match[1];
    }
  }

  return null;
};

// Current YouTube embed URL
const currentYoutubeEmbedUrl = computed(() => {
  const currentVideo = youtubeVideos.value[currentVideoIndex.value];
  if (youtubeVideos.value.length > 0 && currentVideo) {
    return `https://www.youtube.com/embed/${currentVideo.id}`;
  }
  return '';
});

// Video selection options for dropdown
const videoSelectOptions = computed(() => {
  return youtubeVideos.value.map((video, index) => ({
    label: `${index + 1}`,
    value: index,
  }));
});

// Navigation functions
const previousVideo = () => {
  if (currentVideoIndex.value > 0) {
    currentVideoIndex.value--;
  }
};

const nextVideo = () => {
  if (currentVideoIndex.value < youtubeVideos.value.length - 1) {
    currentVideoIndex.value++;
  }
};

// Minimize/Maximize YouTube player
const minimizeYoutubePlayer = () => {
  isYoutubePlayerMinimized.value = !isYoutubePlayerMinimized.value;
};

// Add computed property to check if right drawer has content
const hasRightDrawerContent = computed(() => {
  return (
    showYoutubePlayer.value ||
    (chatSessionDescription.value.qualitative_report.session_summary &&
      chatSessionDescription.value.qualitative_report.session_summary.length > 0) ||
    (currentChatbotDetail.value.type_name === 'ChecklistChatBot' &&
      chatSessionDescription.value.checklist_progress &&
      chatSessionDescription.value.checklist_progress.length > 0)
  );
});

// Watch for changes in right drawer content
watch(hasRightDrawerContent, (hasContent) => {
  if (!hasContent && isRightDrawerOpen.value) {
    isRightDrawerOpen.value = false;
  }
});

// Consultation mailbox interfaces and logic
interface InternalMessage {
  message_id: string;
  session_id: string;
  sender_user_id: string;
  receiver_user_id: string;
  message_content: string;
  message_type: string;
  is_read: boolean;
  is_sent_to_chatbot: boolean;
  // Additional fields for frontend display (derived from joins)
  sender_role?: 'Student' | 'Teacher';
  receiver_role?: 'Student' | 'Teacher';
  created_at?: string;
}

const showConsultationMailbox = ref(false);
const internalMessages = ref<InternalMessage[]>([]);
const newInternalMessage = ref('');
const sendingInternalMessage = ref(false);
const sessionName = ref('');
const unreadInternalMessagesCount = ref(0);
const hasUnreadInternalMessages = computed(() => unreadInternalMessagesCount.value > 0);
const messagesContainer = ref<HTMLElement | null>(null);

// Periodically check for new internal messages
let messagePollingInterval: NodeJS.Timeout | null = null;

// Fetch internal messages for current session
const fetchInternalMessages = async () => {
  try {
    const response = await api.get('/internal-messages', {
      params: {
        session_id: sessionId.value,
      },
    });
    internalMessages.value = response.data;

    // Count unread messages for current user
    unreadInternalMessagesCount.value = internalMessages.value.filter(
      (msg) => !msg.is_read && msg.sender_role !== user.value.role_name,
    ).length;

    // Mark messages as read when opening mailbox
    if (showConsultationMailbox.value) {
      await markMessagesAsRead();
    }
  } catch (error) {
    console.error('Error fetching internal messages:', error);
  }
};

// Mark unread messages as read
const markMessagesAsRead = async () => {
  try {
    await api.put('/internal-messages/mark-read', {
      session_id: sessionId.value,
      user_role: user.value.role_name,
    });
    unreadInternalMessagesCount.value = 0;
  } catch (error) {
    console.error('Error marking messages as read:', error);
  }
};

const openConsultationMailbox = async () => {
  showConsultationMailbox.value = true;
  await fetchInternalMessages();
  await markMessagesAsRead();

  // Scroll to bottom after opening
  await nextTick(() => {
    scrollMessagesToBottom();
  });
};

const scrollMessagesToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

const sendInternalMessage = async () => {
  if (!newInternalMessage.value.trim()) return;

  sendingInternalMessage.value = true;

  try {
    // Determine recipient based on current user role
    const recipientRole = user.value.role_name === 'Student' ? 'Teacher' : 'Student';

    await api.post('/internal-messages', {
      session_id: sessionId.value,
      content: newInternalMessage.value.trim(),
      recipient_role: recipientRole,
    });

    newInternalMessage.value = '';
    await fetchInternalMessages();

    // Scroll to bottom after sending
    await nextTick(() => {
      scrollMessagesToBottom();
    });

    $q.notify({
      type: 'positive',
      message:
        user.value.role_name === 'Student' ? 'Message sent to teacher' : 'Reply sent to student',
    });

    // Create notification for recipient if user is student (raise hand)
    if (user.value.role_name === 'Student') {
      await createRaiseHandNotification();
    }
    // Note: Teacher reply notifications are automatically created by the backend
    // when a teacher sends an internal message to a student
  } catch (error) {
    console.error('Error sending internal message:', error);
    $q.notify({
      type: 'negative',
      message: 'Failed to send message',
    });
  } finally {
    sendingInternalMessage.value = false;
  }
};

const createRaiseHandNotification = async () => {
  try {
    await api.post('/internal-message-notification', {
      session_id: sessionId.value,
      session_name: sessionName.value,
      course_id: courseId.value,
      course_title: courseTitle.value,
      module_id: moduleId.value,
      module_title: moduleTitle.value,
      chatbot_id: selectedChatbotId.value,
    });
  } catch (error) {
    console.error('Error creating raise hand notification:', error);
  }
};

const formatMessageTime = (timestamp: string): string => {
  const date = new Date(timestamp);
  return date.toLocaleString();
};

// Watch for YouTube links in conversation
watch(
  () => currentApiConversation.value,
  (newConversation) => {
    if (!newConversation || newConversation.length === 0) return;

    const foundVideos: Array<{ id: string; url: string; title: string; messageIndex: number }> = [];

    // Search for YouTube links in all messages (preserve chronological order)
    for (let i = 0; i < newConversation.length; i++) {
      const message = newConversation[i];
      if (!message || !message.content) continue;

      // Find URLs in the message
      const urlRegex = /(https?:\/\/[^\s]+)/g;
      const urls = message.content.match(urlRegex);

      if (urls) {
        for (const url of urls) {
          const videoId = extractYoutubeId(url);
          if (videoId) {
            // Check if video is already added (avoid duplicates)
            if (!foundVideos.some((v) => v.id === videoId)) {
              foundVideos.push({
                id: videoId,
                url: url,
                title: `Video from message ${i}`,
                messageIndex: i,
              });
            }
          }
        }
      }
    }

    // Check if there are new videos by comparing with existing ones
    const hasNewVideos =
      foundVideos.length > youtubeVideos.value.length ||
      foundVideos.some(
        (video) => !youtubeVideos.value.some((existing) => existing.id === video.id),
      );

    // Update videos array
    youtubeVideos.value = foundVideos;

    // Handle video player visibility
    if (foundVideos.length > 0) {
      showYoutubePlayer.value = true;
      isYoutubePlayerMinimized.value = false; // Show expanded when new videos are found
      isRightDrawerOpen.value = true; // When there are YouTube links, automatically open the right drawer

      // Always show the latest video (last one in the array)
      if (hasNewVideos || currentVideoIndex.value >= foundVideos.length) {
        currentVideoIndex.value = foundVideos.length - 1; // Always go to the latest video
      }
    } else {
      // No YouTube links found
      showYoutubePlayer.value = false;
      isYoutubePlayerMinimized.value = false; // Reset minimized state
      currentVideoIndex.value = 0;
      // Check if there is any other content, if not, close the right drawer
      if (!hasRightDrawerContent.value) {
        isRightDrawerOpen.value = false;
      }
    }
  },
  { deep: true },
);

onMounted(async () => {
  isFetching.value = true;

  sessionId.value = getStringParam(route.params.sessionId || '');
  selectedSessionId.value = sessionId.value;

  selectedChatbotId.value = getStringParam(route.params.chatbotId || '');

  const courseIdValue = route.query.courseId as string;
  courseId.value = courseIdValue || '';

  const courseTitleValue = route.query.courseTitle as string;
  courseTitle.value = courseTitleValue || 'Unknown Course';

  const moduleIdValue = route.query.moduleId as string;
  moduleId.value = moduleIdValue || '';

  const moduleTitleValue = route.query.moduleTitle as string;
  moduleTitle.value = moduleTitleValue || 'Unknown Module';

  const sessionNameValue = route.query.sessionName as string;
  sessionName.value = sessionNameValue || '';

  try {
    // Parallelize independent API calls
    await Promise.all([fetchUserInfo(), fetchChatbotListByModule()]);

    // These operations depend on the results of fetchChatbotListByModule
    if (selectedChatbotId.value) {
      await fetchChatbotSessions(selectedChatbotId.value);
    }

    // Continue the latest session - must happen after fetching sessions
    if (sessionId.value === 'latest') {
      for (const chatbot of chatbots.value) {
        if (chatbot.chatbot_id === selectedChatbotId.value) {
          // Make sure we have sessions for this chatbot
          if (chatbot.sessions.length === 0) {
            // Fetch sessions first
            await fetchChatbotSessions(selectedChatbotId.value);
          }

          // Check again after fetching
          if (chatbot.sessions.length === 0) {
            $q.notify({
              type: 'info',
              message: 'No previous session available for this chatbot, starting a new session.',
            });
            // Start a new session
            await startNewSession(selectedChatbotId.value, moduleId.value, true);
            break;
          }
          await selectSession(
            selectedChatbotId.value,
            chatbot.sessions[0]?.session_id || '',
            chatbot.sessions[0]?.session_name || '',
          );
          break;
        }
      }
    }

    // Ensure the selected chatbot is expanded
    if (selectedChatbotId.value && !expandedChatbots.value.includes(selectedChatbotId.value)) {
      expandedChatbots.value.push(selectedChatbotId.value);
    }

    // Load chat session description - depends on having a valid sessionId
    await loadChatSessionDescription();

    // Set initialConversationProcessed to true
    initialConversationProcessed.value = true;
  } catch (error) {
    console.error('Error initializing chat layout:', error);
    $q.notify({
      type: 'negative',
      message: 'Failed to initialize chat interface.',
    });
  } finally {
    isFetching.value = false;
  }
});

// Add a function to load chat session description
const loadChatSessionDescription = async () => {
  try {
    const response = await api.get('/chat-session-description', {
      params: {
        session_id: sessionId.value,
      },
    });

    // Handle both new and old format gracefully
    const loadedDescription = response.data.description || {};

    chatSessionDescription.value = {
      checklist_progress: loadedDescription.checklist_progress || [],
      quantitative_report: {
        turn_count: loadedDescription.quantitative_report?.turn_count || 0,
        user_word_count: loadedDescription.quantitative_report?.user_word_count || 0,
        chatbot_word_count: loadedDescription.quantitative_report?.chatbot_word_count || 0,
        conversation_time: loadedDescription.quantitative_report?.conversation_time || 0,
      },
      qualitative_report: {
        session_summary: loadedDescription.qualitative_report?.session_summary || '',
      },
      user_feedback: loadedDescription.user_feedback || {
        rating: loadedDescription.user_feedback?.rating || 0,
        text_feedback: loadedDescription.user_feedback?.text_feedback || '',
      },
    };
  } catch (error) {
    // Check if this is a response format error (expected case)
    if (error instanceof TypeError && error.message.includes('Cannot read properties')) {
      console.debug('Initializing new chat session description format');
    } else {
      // This is an actual error, show notification
      console.error('Error loading chat session description:', error);
      $q.notify({
        type: 'negative',
        message: 'Failed to load chat session description.',
      });
    }
  }
};

watch(route, async (newRoute) => {
  sessionId.value = getStringParam(newRoute.params.sessionId || '');
  selectedSessionId.value = sessionId.value;

  selectedChatbotId.value = getStringParam(newRoute.params.chatbotId || '');

  const courseIdValue = newRoute.query.courseId as string;
  courseId.value = courseIdValue || '';

  const courseTitleValue = newRoute.query.courseTitle as string;
  courseTitle.value = courseTitleValue || 'Unknown Course';

  const moduleIdValue = newRoute.query.moduleId as string;
  moduleId.value = moduleIdValue || '';

  const moduleTitleValue = newRoute.query.moduleTitle as string;
  moduleTitle.value = moduleTitleValue || 'Unknown Module';

  const sessionNameValue = newRoute.query.sessionName as string;
  sessionName.value = sessionNameValue || '';

  try {
    // Parallelize independent API calls
    await Promise.all([fetchUserInfo(), fetchChatbotListByModule()]);

    // These operations depend on the results of fetchChatbotListByModule
    if (selectedChatbotId.value) {
      await fetchChatbotSessions(selectedChatbotId.value);
    }

    // Continue the latest session - must happen after fetching sessions
    if (sessionId.value === 'latest') {
      for (const chatbot of chatbots.value) {
        if (chatbot.chatbot_id === selectedChatbotId.value) {
          // Make sure we have sessions for this chatbot
          if (chatbot.sessions.length === 0) {
            // Fetch sessions first
            await fetchChatbotSessions(selectedChatbotId.value);
          }

          // Check again after fetching
          if (chatbot.sessions.length === 0) {
            $q.notify({
              type: 'info',
              message: 'No previous session available for this chatbot, starting a new session.',
            });
            // Start a new session
            await startNewSession(selectedChatbotId.value, moduleId.value, true);
            break;
          }
          await selectSession(
            selectedChatbotId.value,
            chatbot.sessions[0]?.session_id || '',
            chatbot.sessions[0]?.session_name || '',
          );
          break;
        }
      }
    }

    // Ensure the selected chatbot is expanded
    if (selectedChatbotId.value && !expandedChatbots.value.includes(selectedChatbotId.value)) {
      expandedChatbots.value.push(selectedChatbotId.value);
    }

    // Load chat session description - depends on having a valid sessionId
    await loadChatSessionDescription();
    
    // Fetch internal messages when session changes
    if (sessionId.value && sessionId.value !== 'latest') {
      void fetchInternalMessages();
    }
  } catch (error) {
    console.error('Error updating chat layout on route change:', error);
    $q.notify({
      type: 'negative',
      message: 'Failed to update chat interface.',
    });
  }
});

// Watch for changes to session to fetch new internal messages
watch(sessionId, () => {
  if (sessionId.value && sessionId.value !== 'latest') {
    void fetchInternalMessages();
  }
});

// Setup polling for internal messages
onMounted(() => {
  // Poll for new messages every 5 minutes
  messagePollingInterval = setInterval(() => {
    if (sessionId.value && sessionId.value !== 'latest') {
      void fetchInternalMessages();
    }
  }, 300000);
});

onUnmounted(() => {
  if (messagePollingInterval) {
    clearInterval(messagePollingInterval);
  }
});

const selectSession = async (chatbot: string, sessionId: string, currentSessionName: string) => {
  selectedSessionId.value = sessionId;
  selectedChatbotId.value = chatbot;
  sessionName.value = currentSessionName; // Set the session name for internal messages

  // Ensure the selected chatbot is expanded without closing other chatbots
  if (!expandedChatbots.value.includes(chatbot)) {
    expandedChatbots.value.push(chatbot);
  }

  await router.push({
    path: `/chat/${chatbot}/session/${sessionId}`,
    query: {
      sessionName: currentSessionName,
      courseId: courseId.value,
      courseTitle: courseTitle.value,
      moduleId: moduleId.value,
      moduleTitle: moduleTitle.value,
    },
  });
};

watch(
  currentApiConversation,
  (newValue) => {
    // Check if this chatbot type is a checklist chatbot
    if (currentChatbotDetail.value.type_name === 'ChecklistChatBot') {
      // If the latest conversation message is from the user, get the checklist_progress by API
      if (newValue.length > 0 && newValue[newValue.length - 1]?.role === 'user') {
        const originalChecklistItems = currentChatbotDetail.value.description.checklist_items;
        // Fetch API to get the checklist_progress
        void (async () => {
          try {
            const response = await api.post('/chat-session-checklist-progress', {
              session_id: sessionId.value,
              conversation_list: currentApiConversation.value,
              checklist_items: originalChecklistItems,
            });

            // console.log('Checklist Progress:', response.data);
            // Parse the JSON string
            if (response.data) {
              // Update chatSessionDescription
              chatSessionDescription.value = {
                ...chatSessionDescription.value,
                checklist_progress: response.data,
              };
            }
            // console.log('Chat Session Description:', chatSessionDescription.value);
          } catch (error) {
            console.error('Error fetching checklist progress:', error);
          }
        })();
      }
    }

    // All chatbot types will update the quantitative report
    void (async () => {
      await updateChatSessionDescQuantReport();
    })();
  },
  { deep: true },
);
// Provide fetchSessionSummary to child components
provide('fetchSessionSummary', fetchSessionSummary);
provide('chatSessionDescription', chatSessionDescription);
provide('generateScript', generateScript);

const toggleChatbotExpand = (chatbotId: string, isExpanded: boolean) => {
  if (isExpanded) {
    expandedChatbots.value.push(chatbotId);
  } else {
    expandedChatbots.value = expandedChatbots.value.filter((id) => id !== chatbotId);
  }
};

// Document Preview Functions
const toggleDocumentPreview = () => {
  if (previewState.isVisible) {
    hideDocumentPreview();
  } else if (previewState.htmlContent) {
    previewState.isVisible = true;
  }
};

// Mark unread messages as read
const markMessagesAsRead = async () => {
  try {
    await api.put('/internal-messages/mark-read', {
      session_id: sessionId.value,
      user_role: user.value.role_name,
    });
    unreadInternalMessagesCount.value = 0;
  } catch (error) {
    console.error('Error marking messages as read:', error);
  }
};

const openConsultationMailbox = async () => {
  showConsultationMailbox.value = true;
  await fetchInternalMessages();
  await markMessagesAsRead();

  // Scroll to bottom after opening
  await nextTick(() => {
    scrollMessagesToBottom();
  });
};

const scrollMessagesToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

const sendInternalMessage = async () => {
  if (!newInternalMessage.value.trim()) return;

  sendingInternalMessage.value = true;

  try {
    // Determine recipient based on current user role
    const recipientRole = user.value.role_name === 'Student' ? 'Teacher' : 'Student';

    await api.post('/internal-messages', {
      session_id: sessionId.value,
      content: newInternalMessage.value.trim(),
      recipient_role: recipientRole,
    });

    newInternalMessage.value = '';
    await fetchInternalMessages();

    // Scroll to bottom after sending
    await nextTick(() => {
      scrollMessagesToBottom();
    });

    $q.notify({
      type: 'positive',
      message:
        user.value.role_name === 'Student' ? 'Message sent to teacher' : 'Reply sent to student',
    });

    // Create notification for recipient if user is student (raise hand)
    if (user.value.role_name === 'Student') {
      await createRaiseHandNotification();
    }
    // Note: Teacher reply notifications are automatically created by the backend
    // when a teacher sends an internal message to a student
  } catch (error) {
    console.error('Error sending internal message:', error);
    $q.notify({
      type: 'negative',
      message: 'Failed to send message',
    });
  } finally {
    sendingInternalMessage.value = false;
  }
};

const createRaiseHandNotification = async () => {
  try {
    await api.post('/internal-message-notification', {
      session_id: sessionId.value,
      session_name: sessionName.value,
      course_id: courseId.value,
      course_title: courseTitle.value,
      module_id: moduleId.value,
      module_title: moduleTitle.value,
      chatbot_id: selectedChatbotId.value,
    });
  } catch (error) {
    console.error('Error creating raise hand notification:', error);
  }
};

const formatMessageTime = (timestamp: string): string => {
  const date = new Date(timestamp);
  return date.toLocaleString();
};

// Watch for changes to session to fetch new internal messages
watch(sessionId, () => {
  if (sessionId.value && sessionId.value !== 'latest') {
    void fetchInternalMessages();
  }
});

// Periodically check for new internal messages
let messagePollingInterval: NodeJS.Timeout | null = null;

onMounted(() => {
  // Poll for new messages every 5 minutes
  messagePollingInterval = setInterval(() => {
    if (sessionId.value && sessionId.value !== 'latest') {
      void fetchInternalMessages();
    }
  }, 300000);
});

onUnmounted(() => {
  if (messagePollingInterval) {
    clearInterval(messagePollingInterval);
  }
});
</script>

<style scoped>
.q-drawer {
  width: 300px;
}

.youtube-container {
  position: relative;
  width: 100%;
  padding-top: 56.25%; /* 16:9 Aspect Ratio */
}

.youtube-iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 4px;
}
</style>

<style>
.sessionsummary-container h1 {
  font-size: 24px;
  font-weight: bold;
  line-height: 1.5rem;
  margin: 1.5rem 0;
}

.sessionsummary-container h2 {
  font-size: 22px;
  font-weight: bold;
  line-height: 1.4rem;
  margin: 1.4rem 0;
}

.sessionsummary-container h3 {
  font-size: 20px;
  font-weight: bold;
  line-height: 1.3rem;
  margin: 1.3rem 0;
}

.sessionsummary-container h4 {
  font-size: 18px;
  font-weight: bold;
  line-height: 1.2rem;
  margin: 1.2rem 0;
}

.sessionsummary-container h5 {
  font-size: 16px;
  font-weight: bold;
  line-height: 1.1rem;
  margin: 1.1rem 0;
}

.sessionsummary-container h6 {
  font-size: 14px;
  font-weight: bold;
  line-height: 1rem;
  margin: 1rem 0;
}
</style>
