<template>
  <q-layout view="hHh LpR lfr">
    <q-header elevated class="bg-white text-dark">
      <q-toolbar>
        <q-toolbar-title class="text-weight-bold">
          <q-btn
            flat
            no-caps
            label="Bytewise"
            to="/teacher/homepage"
            class="text-weight-bold"
            style="font-size: 1.3125rem; padding: 5px"
          />
        </q-toolbar-title>
      </q-toolbar>
    </q-header>

    <q-page-container>
      <div v-if="isLoading"></div>
      <router-view v-else />
    </q-page-container>
  </q-layout>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useQuasar } from 'quasar';
import { api } from 'boot/axios';

const router = useRouter();
const $q = useQuasar();

interface User {
  full_name: string;
  email: string;
  role_name: string;
}

const user = ref<User | null>(null);
const isLoading = ref(true);

const fetchUserData = async () => {
  try {
    const response = await api.get('/user-info');
    user.value = response.data;

    // Check if user is a teacher and in whitelist
    if (!user.value) {
      $q.notify({
        type: 'negative',
        message: 'User data not found',
      });
      await router.push('/login');
      return;
    }
  } catch {
    $q.notify({
      type: 'negative',
      message: 'Failed to fetch user data',
    });
    await router.push('/login');
  } finally {
    isLoading.value = false;
  }
};

onMounted(async () => {
  await fetchUserData();
});
</script>
